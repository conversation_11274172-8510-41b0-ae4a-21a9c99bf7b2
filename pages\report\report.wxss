/* pages/report/report.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.claims-list {
  padding: 20rpx 30rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.claim-item {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.claim-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.claim-id {
  font-size: 30rpx;
  font-weight: 500;
}

.claim-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status-1 {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-2 {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-3 {
  background-color: #f9f0ff;
  color: #722ed1;
}

.status-4 {
  background-color: #fcffe6;
  color: #7cb305;
}

.status-5 {
  background-color: #e6fffb;
  color: #13c2c2;
}

.status-6 {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-7 {
  background-color: #fff1f0;
  color: #f5222d;
}

.claim-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.claim-type, .claim-date {
  margin-right: 30rpx;
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.claim-desc {
  font-size: 26rpx;
  color: #666;
}

.label {
  color: #999;
  margin-right: 10rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.float-button {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 240rpx;
  height: 80rpx;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.4);
  z-index: 100;
}

.float-button text:first-child {
  font-size: 40rpx;
  margin-right: 10rpx;
}

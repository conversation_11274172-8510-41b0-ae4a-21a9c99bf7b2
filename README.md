# 现代财险理赔系统微信小程序

## 项目概述

现代财险理赔助手是一款专为现代财产保险股份有限公司理赔设计的微信小程序，提供自助报案、查勘和定损三大核心功能模块，为用户提供便捷、高效、透明的理赔服务体验。

## 功能特性

### 🚗 自助报案
- 微信授权登录
- 保单信息绑定
- 出险信息填写
- 影像资料上传
- 定位功能
- 报案状态查询

### 🔍 查勘管理
- 查勘预约
- 远程查勘（视频通话）
- 现场查勘
- 查勘进度跟踪
- 查勘结果查看

### 💰 定损评估
- AI智能定损
- 定损项目明细
- 定损金额计算
- 电子签名确认
- 异议提交

### 👤 用户中心
- 个人信息管理
- 保单管理
- 理赔记录查询
- 客服支持

## 技术架构

### 前端技术栈
- **框架**: 微信小程序原生框架
- **UI组件库**: Vant Weapp
- **样式**: WXSS + CSS变量
- **状态管理**: 全局数据管理

### 项目结构
```
claimease/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── project.config.json   # 项目配置文件
├── pages/                # 页面目录
│   ├── index/           # 首页
│   ├── login/           # 登录页
│   ├── user/            # 用户中心
│   ├── report/          # 报案模块
│   │   ├── report.js    # 报案列表
│   │   ├── form.js      # 报案表单
│   │   ├── success.js   # 报案成功
│   │   └── detail.js    # 报案详情
│   ├── survey/          # 查勘模块
│   │   ├── survey.js    # 查勘列表
│   │   ├── appointment.js # 查勘预约
│   │   ├── remote.js    # 远程查勘
│   │   └── result.js    # 查勘结果
│   └── loss/            # 定损模块
│       ├── loss.js      # 定损列表
│       ├── assessment.js # 定损评估
│       └── confirm.js   # 定损确认
└── images/              # 图片资源
    ├── tabbar/          # 底部导航图标
    ├── icons/           # 功能图标
    ├── banner/          # 轮播图
    └── sample/          # 示例图片
```

## 安装与运行

### 环境要求
- 微信开发者工具
- Node.js (可选，用于包管理)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd claimease
   ```

2. **安装依赖**
   ```bash
   npm install @vant/weapp
   ```

3. **配置小程序**
   - 在微信开发者工具中导入项目
   - 修改 `project.config.json` 中的 `appid` 为你的小程序ID
   - 配置服务器域名（如果有后端接口）

4. **运行项目**
   - 在微信开发者工具中点击"编译"
   - 在模拟器或真机上预览

## 配置说明

### 1. Vant Weapp 组件库配置

在 `app.json` 中已配置了常用的 Vant 组件：

```json
{
  "usingComponents": {
    "van-button": "@vant/weapp/button/index",
    "van-cell": "@vant/weapp/cell/index",
    "van-field": "@vant/weapp/field/index",
    // ... 其他组件
  }
}
```

### 2. 全局样式配置

在 `app.wxss` 中定义了CSS变量和通用样式：

```css
page {
  --primary-color: #1890FF;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  // ... 其他变量
}
```

### 3. 全局数据配置

在 `app.js` 中配置了全局数据：

```javascript
globalData: {
  userInfo: null,
  isLogin: false,
  token: '',
  baseUrl: 'https://api.claimease.com',
  // ... 其他全局数据
}
```

## 页面功能说明

### 首页 (pages/index)
- 用户信息展示
- 轮播图
- 快捷功能入口
- 最近理赔记录

### 自助报案 (pages/report)
- **report**: 报案列表页，支持状态筛选
- **form**: 报案表单页，分步骤填写
- **success**: 报案成功页
- **detail**: 报案详情页，显示完整流程

### 查勘管理 (pages/survey)
- **survey**: 查勘列表页
- **appointment**: 查勘预约页
- **remote**: 远程查勘页，支持视频通话
- **result**: 查勘结果页

### 定损评估 (pages/loss)
- **loss**: 定损列表页
- **assessment**: 定损评估页，支持AI分析
- **confirm**: 定损确认页，支持电子签名

### 用户中心 (pages/user)
- 个人信息展示
- 统计数据
- 功能入口
- 设置选项

## 开发指南

### 1. 添加新页面

1. 在 `pages` 目录下创建新的页面文件夹
2. 创建 `.js`, `.wxml`, `.wxss` 文件
3. 在 `app.json` 的 `pages` 数组中添加页面路径

### 2. 使用全局数据

```javascript
const app = getApp()

// 获取全局数据
const userInfo = app.globalData.userInfo

// 设置全局数据
app.globalData.isLogin = true
```

### 3. 页面间传参

```javascript
// 跳转并传参
wx.navigateTo({
  url: '/pages/detail/detail?id=123'
})

// 接收参数
onLoad: function (options) {
  const id = options.id
}
```

### 4. 网络请求

```javascript
wx.request({
  url: app.globalData.baseUrl + '/api/claims',
  method: 'GET',
  header: {
    'Authorization': 'Bearer ' + app.globalData.token
  },
  success: (res) => {
    console.log(res.data)
  }
})
```

## 注意事项

### 1. 图片资源
- 项目中的图片路径需要替换为实际的图片文件
- 建议使用 CDN 或云存储服务托管图片

### 2. 接口对接
- 当前使用模拟数据，需要替换为实际的后端接口
- 注意配置服务器域名白名单

### 3. 权限申请
- 定位功能需要申请地理位置权限
- 相机功能需要申请摄像头权限
- 相册功能需要申请相册权限

### 4. 性能优化
- 图片压缩和懒加载
- 分包加载
- 代码分割

## 部署发布

### 1. 代码审核
- 检查代码质量
- 测试所有功能
- 优化性能

### 2. 提交审核
- 在微信开发者工具中点击"上传"
- 在微信公众平台提交审核
- 等待审核通过

### 3. 发布上线
- 审核通过后点击"发布"
- 配置版本信息
- 监控线上运行状态

## 联系方式

如有问题或建议，请联系开发团队。

---

© 2023 现代财险 保险理赔助手

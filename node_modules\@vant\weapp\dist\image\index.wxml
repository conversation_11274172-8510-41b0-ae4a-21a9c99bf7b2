<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./index.wxs" module="computed" />

<view
  style="{{ computed.rootStyle({ width, height, radius }) }}"
  class="custom-class {{ utils.bem('image', { round })}}"
  bind:tap="onClick"
>
  <image
    wx:if="{{ !error }}"
    src="{{ src }}"
    mode="{{ computed.mode(fit) }}"
    lazy-load="{{ lazyLoad }}"
    webp="{{ webp }}"
    class="image-class van-image__img"
    show-menu-by-longpress="{{ showMenuByLongpress }}"
    bind:load="onLoad"
    bind:error="onError"
  />

  <view
    wx:if="{{ loading && showLoading }}"
    class="loading-class van-image__loading"
  >
    <slot wx:if="{{ useLoadingSlot }}" name="loading" />
    <van-icon wx:else name="photo" custom-class="van-image__loading-icon" />
  </view>
  <view
    wx:if="{{ error && showError }}"
    class="error-class van-image__error"
  >
    <slot wx:if="{{ useErrorSlot }}" name="error" />
    <van-icon wx:else name="photo-fail" custom-class="van-image__error-icon" />
  </view>
</view>

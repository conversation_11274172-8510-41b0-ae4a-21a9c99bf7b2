// pages/loss/confirm.js
Page({
  data: {
    claimId: '',
    loading: true,
    lossResult: null,
    showDisputePopup: false,
    disputeReason: '',
    disputeContent: '',
    submitting: false,
    showSignaturePopup: false,
    signatureUrl: '',
    isDrawing: false,
    startX: 0,
    startY: 0,
    canvasContext: null,
    canvasWidth: 0,
    canvasHeight: 0
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        claimId: options.id
      })
      
      this.loadLossResult(options.id)
    }
  },
  
  onReady: function () {
    // 获取画布上下文
    const query = wx.createSelectorQuery()
    query.select('#signature-canvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0]) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')
          
          const dpr = wx.getSystemInfoSync().pixelRatio
          canvas.width = res[0].width * dpr
          canvas.height = res[0].height * dpr
          ctx.scale(dpr, dpr)
          
          this.setData({
            canvasContext: ctx,
            canvasWidth: res[0].width,
            canvasHeight: res[0].height
          })
          
          // 设置画布背景色
          ctx.fillStyle = '#ffffff'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          
          // 设置画笔样式
          ctx.strokeStyle = '#000000'
          ctx.lineWidth = 4
          ctx.lineCap = 'round'
          ctx.lineJoin = 'round'
        }
      })
  },
  
  loadLossResult: function (id) {
    this.setData({
      loading: true
    })
    
    // 模拟获取定损结果
    setTimeout(() => {
      // 模拟数据
      const lossResult = {
        id: 'LS' + id.substring(2),
        claimId: id,
        assessmentTime: '2023-05-18 15:30',
        assessor: 'AI智能定损系统',
        status: 'completed',
        items: [
          {
            id: 1,
            name: '右前车门更换',
            price: 2800,
            quantity: 1,
            amount: 2800,
            remark: '原厂件'
          },
          {
            id: 2,
            name: '前保险杠修复',
            price: 1200,
            quantity: 1,
            amount: 1200,
            remark: '局部喷漆'
          },
          {
            id: 3,
            name: '工时费',
            price: 100,
            quantity: 8,
            amount: 800,
            remark: '标准工时'
          }
        ],
        totalAmount: 4800,
        remarks: '定损结果已经过专业技术人员审核，符合行业标准。',
        images: [
          { url: '/images/sample/car_damage1.jpg', name: '车辆右前门受损' },
          { url: '/images/sample/car_damage2.jpg', name: '前保险杠刮擦' }
        ]
      }
      
      this.setData({
        lossResult,
        loading: false
      })
    }, 1500)
  },
  
  previewImage: function (e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.lossResult.images.map(img => img.url)
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },
  
  showDispute: function () {
    this.setData({
      showDisputePopup: true
    })
  },
  
  onDisputeClose: function () {
    this.setData({
      showDisputePopup: false
    })
  },
  
  selectDisputeReason: function (e) {
    this.setData({
      disputeReason: e.currentTarget.dataset.reason
    })
  },
  
  inputDisputeContent: function (e) {
    this.setData({
      disputeContent: e.detail.value
    })
  },
  
  submitDispute: function () {
    const { disputeReason, disputeContent } = this.data
    
    if (!disputeReason) {
      wx.showToast({
        title: '请选择异议原因',
        icon: 'none'
      })
      return
    }
    
    if (!disputeContent) {
      wx.showToast({
        title: '请填写异议内容',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      submitting: true
    })
    
    // 模拟提交
    setTimeout(() => {
      this.setData({
        submitting: false,
        showDisputePopup: false
      })
      
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 2000)
  },
  
  showSignature: function () {
    this.setData({
      showSignaturePopup: true
    })
  },
  
  onSignatureClose: function () {
    this.setData({
      showSignaturePopup: false
    })
  },
  
  touchStart: function (e) {
    const { x, y } = e.touches[0]
    this.setData({
      isDrawing: true,
      startX: x,
      startY: y
    })
    
    const { canvasContext } = this.data
    canvasContext.beginPath()
    canvasContext.moveTo(x, y)
  },
  
  touchMove: function (e) {
    if (!this.data.isDrawing) return
    
    const { x, y } = e.touches[0]
    const { canvasContext } = this.data
    
    canvasContext.lineTo(x, y)
    canvasContext.stroke()
    
    this.setData({
      startX: x,
      startY: y
    })
  },
  
  touchEnd: function () {
    this.setData({
      isDrawing: false
    })
  },
  
  clearSignature: function () {
    const { canvasContext, canvasWidth, canvasHeight } = this.data
    
    canvasContext.clearRect(0, 0, canvasWidth, canvasHeight)
    canvasContext.fillStyle = '#ffffff'
    canvasContext.fillRect(0, 0, canvasWidth, canvasHeight)
  },
  
  confirmSignature: function () {
    // 模拟保存签名
    this.setData({
      signatureUrl: '/images/sample/signature.png',
      showSignaturePopup: false
    })
    
    wx.showToast({
      title: '签名已保存',
      icon: 'success'
    })
  },
  
  confirmLoss: function () {
    if (!this.data.signatureUrl) {
      wx.showToast({
        title: '请先进行电子签名',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      submitting: true
    })
    
    // 模拟提交
    setTimeout(() => {
      this.setData({
        submitting: false
      })
      
      wx.showToast({
        title: '确认成功',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 2000)
  },
  
  backToDetail: function () {
    wx.navigateBack()
  }
})

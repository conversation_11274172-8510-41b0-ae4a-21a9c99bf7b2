<!--pages/login/login.wxml-->
<view class="container">
  <view class="login-header">
    <image src="/images/logo.png" mode="aspectFit" class="logo"></image>
    <text class="title">现代财险理赔助手</text>
    <text class="subtitle">便捷、高效、透明的理赔服务</text>
  </view>

  <view class="login-form">
    <view class="form-item">
      <text class="form-label">手机号</text>
      <view class="input-container">
        <text class="prefix">+86</text>
        <input type="number" placeholder="请输入手机号" maxlength="11" bindinput="inputPhone" value="{{phone}}" />
      </view>
    </view>

    <view class="form-item">
      <text class="form-label">验证码</text>
      <view class="input-container verification-code">
        <input type="number" placeholder="请输入验证码" maxlength="6" bindinput="inputCode" value="{{code}}" />
        <view class="code-button {{!canGetCode ? 'disabled' : ''}}" bindtap="getVerificationCode">
          <text>{{canGetCode ? '获取验证码' : countdown + '秒后重试'}}</text>
        </view>
      </view>
    </view>

    <view class="agreement">
      <view class="checkbox" bindtap="toggleAgreement">
        <view class="checkbox-inner {{agreementChecked ? 'checked' : ''}}"></view>
      </view>
      <view class="agreement-text">
        <text>我已阅读并同意</text>
        <text class="link" bindtap="navigateToAgreement">《用户协议与隐私政策》</text>
      </view>
    </view>

    <button class="login-button" bindtap="login">登录</button>

    <view class="other-login">
      <view class="divider">
        <view class="line"></view>
        <text>其他登录方式</text>
        <view class="line"></view>
      </view>

      <view class="login-methods">
        <view class="login-method">
          <image src="/images/icons/wechat.png" mode="aspectFit"></image>
          <text>微信</text>
        </view>
      </view>
    </view>
  </view>

  <view class="login-footer">
    <text>© 2023 现代财险 保险理赔助手</text>
  </view>
</view>

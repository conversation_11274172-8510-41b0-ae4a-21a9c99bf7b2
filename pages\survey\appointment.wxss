/* pages/survey/appointment.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.claim-info {
  background-color: #fff;
  padding: 30rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.claim-id, .claim-type {
  margin-bottom: 10rpx;
}

.claim-desc {
  
}

.form-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.placeholder {
  color: #999;
}

input {
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
}

textarea {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  box-sizing: border-box;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.error-message {
  color: var(--error-color);
  font-size: 24rpx;
  margin-top: 10rpx;
}

.notice-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.submit-section {
  padding: 30rpx;
}

.submit-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}

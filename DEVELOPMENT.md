# ClaimEase 开发指南

## 快速开始

### 1. 环境准备

确保你已经安装了以下工具：
- 微信开发者工具（最新版本）
- Node.js（可选，用于包管理）

### 2. 项目导入

1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录 `claimease`
4. 输入你的小程序AppID（测试可以使用测试号）
5. 点击"导入"

### 3. 依赖安装

如果需要使用npm包管理：

```bash
# 进入项目目录
cd claimease

# 安装依赖
npm install

# 构建npm包（在微信开发者工具中执行）
# 工具 -> 构建npm
```

### 4. 项目配置

修改 `project.config.json` 中的配置：

```json
{
  "appid": "你的小程序AppID",
  "projectname": "ClaimEase"
}
```

## 项目结构详解

```
claimease/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序全局配置
├── app.wxss              # 全局样式
├── sitemap.json          # 站点地图
├── project.config.json   # 项目配置
├── package.json          # npm配置
├── README.md             # 项目说明
├── DEVELOPMENT.md        # 开发指南
├── pages/                # 页面目录
│   ├── index/           # 首页
│   │   ├── index.js     # 页面逻辑
│   │   ├── index.wxml   # 页面结构
│   │   └── index.wxss   # 页面样式
│   ├── login/           # 登录页
│   ├── user/            # 用户中心
│   ├── report/          # 报案模块
│   │   ├── report.*     # 报案列表
│   │   ├── form.*       # 报案表单
│   │   ├── success.*    # 报案成功
│   │   └── detail.*     # 报案详情
│   ├── survey/          # 查勘模块
│   │   ├── survey.*     # 查勘列表
│   │   ├── appointment.* # 查勘预约
│   │   ├── remote.*     # 远程查勘
│   │   └── result.*     # 查勘结果
│   └── loss/            # 定损模块
│       ├── loss.*       # 定损列表
│       ├── assessment.* # 定损评估
│       └── confirm.*    # 定损确认
├── utils/               # 工具函数
│   ├── util.js          # 通用工具
│   └── request.js       # 网络请求封装
├── api/                 # API接口定义
│   └── index.js         # 接口集合
└── images/              # 图片资源
    ├── tabbar/          # 底部导航图标
    ├── icons/           # 功能图标
    ├── banner/          # 轮播图
    └── sample/          # 示例图片
```

## 开发规范

### 1. 代码规范

#### JavaScript规范
- 使用ES6+语法
- 使用const/let替代var
- 函数命名使用驼峰命名法
- 常量使用大写字母和下划线

```javascript
// 好的示例
const getUserInfo = () => {
  const API_BASE_URL = 'https://api.example.com'
  // ...
}

// 避免
var getUserInfo = function() {
  var apiBaseUrl = 'https://api.example.com'
  // ...
}
```

#### WXML规范
- 标签名使用小写
- 属性使用双引号
- 自闭合标签使用简写

```xml
<!-- 好的示例 -->
<view class="container">
  <image src="{{imageUrl}}" mode="aspectFit" />
</view>

<!-- 避免 -->
<view class='container'>
  <image src='{{imageUrl}}' mode='aspectFit'></image>
</view>
```

#### WXSS规范
- 使用CSS变量定义主题色
- 类名使用短横线命名法
- 避免使用!important

```css
/* 好的示例 */
.user-info {
  color: var(--primary-color);
  font-size: 28rpx;
}

/* 避免 */
.userInfo {
  color: #1890FF !important;
  font-size: 28rpx;
}
```

### 2. 文件命名规范

- 页面文件：使用小写字母和短横线，如 `user-profile`
- 组件文件：使用驼峰命名法，如 `UserCard`
- 工具文件：使用小写字母，如 `util.js`
- 常量文件：使用大写字母，如 `CONSTANTS.js`

### 3. 注释规范

```javascript
/**
 * 获取用户信息
 * @param {string} userId 用户ID
 * @returns {Promise<Object>} 用户信息
 */
const getUserInfo = async (userId) => {
  // 发送请求获取用户信息
  const response = await userApi.getUserInfo(userId)
  return response.data
}
```

## 开发流程

### 1. 新功能开发

1. **需求分析**：明确功能需求和用户场景
2. **设计评审**：确定UI设计和交互流程
3. **技术方案**：制定技术实现方案
4. **编码实现**：按照规范编写代码
5. **自测验证**：完成功能自测
6. **代码审查**：提交代码审查
7. **测试验证**：配合测试完成验证
8. **发布上线**：发布到生产环境

### 2. Bug修复

1. **问题复现**：确认问题现象和复现步骤
2. **原因分析**：分析问题根本原因
3. **修复方案**：制定修复方案
4. **代码修改**：实施修复
5. **验证测试**：验证修复效果
6. **发布修复**：发布修复版本

### 3. 版本发布

1. **功能测试**：完成所有功能测试
2. **性能测试**：检查性能指标
3. **兼容性测试**：验证设备兼容性
4. **代码审查**：完成代码审查
5. **版本打包**：构建发布版本
6. **提交审核**：提交微信审核
7. **发布上线**：审核通过后发布

## 调试技巧

### 1. 控制台调试

```javascript
// 使用console.log输出调试信息
console.log('用户信息:', userInfo)

// 使用console.error输出错误信息
console.error('请求失败:', error)

// 使用console.table输出表格数据
console.table(claimList)
```

### 2. 网络请求调试

在微信开发者工具中：
1. 打开"调试器"面板
2. 切换到"Network"标签
3. 查看网络请求和响应

### 3. 存储调试

```javascript
// 查看本地存储
console.log('本地存储:', wx.getStorageSync('userInfo'))

// 清除本地存储
wx.clearStorageSync()
```

### 4. 页面调试

```javascript
// 在页面生命周期中添加调试信息
Page({
  onLoad(options) {
    console.log('页面加载参数:', options)
  },
  
  onShow() {
    console.log('页面显示')
  }
})
```

## 性能优化

### 1. 图片优化

- 使用WebP格式图片
- 压缩图片大小
- 使用CDN加速
- 实现图片懒加载

```javascript
// 图片懒加载示例
const lazyLoadImage = (imageSrc) => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(imageSrc)
    img.src = imageSrc
  })
}
```

### 2. 代码优化

- 避免频繁的setData操作
- 使用节流和防抖
- 减少不必要的计算

```javascript
// 优化setData
const updateData = debounce((data) => {
  this.setData(data)
}, 100)
```

### 3. 网络优化

- 合并网络请求
- 使用缓存机制
- 实现请求重试

```javascript
// 请求缓存示例
const cache = new Map()

const getCachedData = async (key, fetcher) => {
  if (cache.has(key)) {
    return cache.get(key)
  }
  
  const data = await fetcher()
  cache.set(key, data)
  return data
}
```

## 常见问题

### 1. 页面跳转问题

**问题**：页面跳转失败
**解决**：检查页面路径是否正确，是否在app.json中注册

### 2. 网络请求问题

**问题**：网络请求失败
**解决**：检查域名是否在小程序后台配置，检查请求参数格式

### 3. 图片显示问题

**问题**：图片无法显示
**解决**：检查图片路径，确保图片格式支持，检查网络权限

### 4. 数据绑定问题

**问题**：数据更新但页面不刷新
**解决**：使用setData更新数据，检查数据路径是否正确

## 部署发布

### 1. 开发环境

- 本地开发调试
- 使用模拟器测试
- 真机预览测试

### 2. 测试环境

- 提交测试版本
- 完成功能测试
- 修复发现的问题

### 3. 生产环境

- 提交审核版本
- 等待微信审核
- 审核通过后发布

### 4. 发布检查清单

- [ ] 功能完整性测试
- [ ] 性能指标检查
- [ ] 兼容性验证
- [ ] 安全性检查
- [ ] 用户体验测试
- [ ] 代码质量审查

## 技术支持

如果在开发过程中遇到问题，可以通过以下方式获取帮助：

1. **查看文档**：仔细阅读微信小程序官方文档
2. **搜索问题**：在开发者社区搜索相关问题
3. **提交Issue**：在项目仓库提交问题反馈
4. **联系团队**：联系开发团队获取支持

---

祝你开发愉快！🎉

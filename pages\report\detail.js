// pages/report/detail.js
const app = getApp()

Page({
  data: {
    claimId: '',
    claimDetail: null,
    loading: true,
    activeNames: ['1'], // 默认展开第一个折叠面板
    progressSteps: [
      { text: '已报案', desc: '2023-05-15 10:30' },
      { text: '查勘中', desc: '2023-05-16 14:20' },
      { text: '定损中', desc: '' },
      { text: '理赔审核', desc: '' },
      { text: '赔付完成', desc: '' }
    ],
    activeStep: 1, // 当前进行到的步骤
    showImagePreview: false,
    previewImages: [],
    currentPreviewIndex: 0
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        claimId: options.id
      })

      this.loadClaimDetail(options.id)
    }
  },

  loadClaimDetail: function (id) {
    this.setData({
      loading: true
    })

    // 模拟获取理赔详情
    setTimeout(() => {
      // 模拟数据
      const claimDetail = {
        id: id,
        status: 2, // 查勘中
        policy: {
          id: 'P20230001',
          name: '车险-京A12345',
          company: '现代财险',
          expireDate: '2023-12-31'
        },
        accident: {
          date: '2023-05-15',
          time: '10:30',
          location: '北京市朝阳区三里屯太古里',
          type: '车险',
          description: '交通事故车辆受损，右前车门凹陷，前保险杠刮擦。'
        },
        images: [
          { url: '/images/sample/car_damage1.jpg', name: '车辆受损照片1' },
          { url: '/images/sample/car_damage2.jpg', name: '车辆受损照片2' },
          { url: '/images/sample/accident_scene.jpg', name: '事故现场' }
        ],
        survey: {
          status: 1, // 1-待预约, 2-已预约, 3-已完成
          appointmentTime: '',
          surveyor: '',
          result: ''
        },
        loss: {
          status: 0, // 0-未开始, 1-评估中, 2-已完成
          amount: 0,
          items: []
        },
        timeline: [
          {
            time: '2023-05-15 10:30',
            title: '提交报案',
            content: '您已成功提交报案申请，报案号：' + id
          },
          {
            time: '2023-05-15 11:15',
            title: '报案受理',
            content: '您的报案已受理，理赔专员将尽快与您联系'
          },
          {
            time: '2023-05-16 14:20',
            title: '安排查勘',
            content: '已安排查勘员与您联系，请保持电话畅通'
          }
        ]
      }

      this.setData({
        claimDetail,
        loading: false,
        activeStep: claimDetail.status - 1,
        previewImages: claimDetail.images.map(img => img.url)
      })
    }, 1500)
  },

  onCollapseChange: function (e) {
    this.setData({
      activeNames: e.detail
    })
  },

  previewImage: function (e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.previewImages

    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },

  makeAppointment: function () {
    wx.navigateTo({
      url: `/pages/survey/appointment?id=${this.data.claimId}`
    })
  },

  startRemoteSurvey: function () {
    wx.navigateTo({
      url: `/pages/survey/remote?id=${this.data.claimId}`
    })
  },

  viewSurveyResult: function () {
    wx.navigateTo({
      url: `/pages/survey/result?id=${this.data.claimId}`
    })
  },

  startLossAssessment: function () {
    wx.navigateTo({
      url: `/pages/loss/assessment?id=${this.data.claimId}`
    })
  },

  viewLossResult: function () {
    wx.navigateTo({
      url: `/pages/loss/confirm?id=${this.data.claimId}`
    })
  },

  contactCustomerService: function () {
    // 拨打客服电话
    wx.makePhoneCall({
      phoneNumber: '4001234567'
    })
  },

  copyClaimId: function () {
    wx.setClipboardData({
      data: this.data.claimId,
      success: function () {
        wx.showToast({
          title: '报案号已复制',
          icon: 'success'
        })
      }
    })
  }
})

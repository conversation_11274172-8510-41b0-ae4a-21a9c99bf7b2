/* pages/loss/confirm.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.claim-id {
  font-size: 28rpx;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.result-card {
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-tag {
  background-color: #f6ffed;
  color: #52c41a;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.loss-id {
  font-size: 26rpx;
  color: #666;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #999;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-value.amount {
  color: #f5222d;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.result-table {
  border: 1px solid #e8e8e8;
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f5f5f5;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.th {
  padding: 20rpx 10rpx;
  text-align: center;
}

.th-name {
  flex: 2;
  text-align: left;
}

.th-price, .th-quantity, .th-amount {
  flex: 1;
}

.table-body {
  
}

.table-row {
  display: flex;
  border-top: 1px solid #e8e8e8;
  font-size: 28rpx;
  color: #333;
}

.td {
  padding: 20rpx 10rpx;
  text-align: center;
}

.td-name {
  flex: 2;
  text-align: left;
  display: flex;
  flex-direction: column;
}

.td-remark {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.td-price, .td-quantity, .td-amount {
  flex: 1;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  font-size: 28rpx;
  font-weight: 500;
}

.total-amount {
  color: #f5222d;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 30%;
  margin-right: 3%;
  margin-bottom: 20rpx;
}

.image-item image {
  width: 100%;
  height: 160rpx;
  border-radius: var(--border-radius-base);
}

.image-name {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remarks {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.signature-section {
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.signature-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.signature-container {
  height: 200rpx;
  border: 1px dashed #ccc;
  border-radius: var(--border-radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.signature-image {
  width: 100%;
  height: 100%;
}

.signature-placeholder {
  color: #999;
  font-size: 28rpx;
}

.action-buttons {
  padding: 30rpx;
}

.btn-primary, .btn-secondary {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

/* 异议弹窗 */
.dispute-popup {
  padding: 30rpx;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 30rpx;
}

.dispute-form {
  
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.reason-list {
  display: flex;
  flex-wrap: wrap;
}

.reason-item {
  width: calc(50% - 20rpx);
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f5f5f5;
  border-radius: var(--border-radius-base);
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.reason-item:nth-child(2n) {
  margin-right: 0;
}

.reason-item.active {
  background-color: var(--primary-color);
  color: #fff;
}

textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  box-sizing: border-box;
  font-size: 28rpx;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.popup-buttons {
  display: flex;
  margin-top: 40rpx;
}

.popup-btn-cancel, .popup-btn-confirm {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
}

.popup-btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.popup-btn-confirm {
  background-color: var(--primary-color);
  color: #fff;
}

/* 签名弹窗 */
.signature-popup {
  padding: 30rpx;
}

.signature-canvas-container {
  width: 100%;
  height: 400rpx;
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius-base);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.signature-canvas {
  width: 100%;
  height: 100%;
}

.signature-tips {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

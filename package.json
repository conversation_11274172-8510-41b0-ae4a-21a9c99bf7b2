{"name": "hyundai-insurance-claims", "version": "1.0.0", "description": "现代财险理赔系统微信小程序", "main": "app.js", "scripts": {"dev": "echo '请在微信开发者工具中打开项目'", "build": "echo '请在微信开发者工具中构建项目'", "preview": "echo '请在微信开发者工具中预览项目'"}, "keywords": ["微信小程序", "保险理赔", "自助报案", "查勘", "定损"], "author": "现代财险技术团队", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/claimease.git"}, "bugs": {"url": "https://github.com/your-username/claimease/issues"}, "homepage": "https://github.com/your-username/claimease#readme", "dependencies": {"@vant/weapp": "^1.11.7"}}
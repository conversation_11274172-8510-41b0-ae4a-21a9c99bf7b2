<!--pages/survey/remote.wxml-->
<view class="container">
  <view class="header">
    <text class="title">远程查勘</text>
    <text class="claim-id">报案号：{{ claimId }}</text>
  </view>
  
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading color="#1890FF" size="24px">加载中...</van-loading>
  </view>
  
  <block wx:else>
    <!-- 未连接状态 -->
    <view class="connect-section" wx:if="{{ !connected }}">
      <view class="connect-info">
        <image src="/images/icons/video_call.png" class="connect-icon"></image>
        <text class="connect-title">远程视频查勘</text>
        <text class="connect-desc">查勘员将通过视频指导您完成查勘</text>
      </view>
      
      <button class="connect-button" bindtap="startCall" loading="{{ connecting }}" disabled="{{ connecting }}">
        {{ connecting ? '连接中...' : '开始视频查勘' }}
      </button>
      
      <!-- 查勘指南 -->
      <view class="guide-section" wx:if="{{ showGuide }}">
        <view class="guide-header">
          <text class="guide-title">查勘指南</text>
          <view class="guide-close" bindtap="closeGuide">×</view>
        </view>
        <view class="guide-content">
          <view class="guide-item" wx:for="{{ guideSteps }}" wx:key="index">
            <text class="guide-number">{{ index + 1 }}</text>
            <text class="guide-text">{{ item }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 已连接状态 -->
    <view class="call-section" wx:else>
      <!-- 视频区域 -->
      <view class="video-container">
        <image src="/images/sample/video_placeholder.jpg" class="main-video"></image>
        <view class="self-video-container">
          <image src="/images/sample/self_video.jpg" class="self-video"></image>
        </view>
        <view class="call-info">
          <text class="call-duration">{{ formatDuration(callDuration) }}</text>
        </view>
        <view class="call-controls">
          <view class="control-button" bindtap="captureImage">
            <image src="/images/icons/camera.png"></image>
            <text>截图</text>
          </view>
          <view class="control-button control-end" bindtap="endCall">
            <image src="/images/icons/end_call.png"></image>
            <text>结束</text>
          </view>
          <view class="control-button">
            <image src="/images/icons/switch_camera.png"></image>
            <text>切换</text>
          </view>
        </view>
      </view>
      
      <!-- 截图列表 -->
      <view class="captures-section" wx:if="{{ capturedImages.length > 0 }}">
        <view class="section-title">已截图 ({{ capturedImages.length }})</view>
        <scroll-view scroll-x class="captures-list">
          <view 
            class="capture-item" 
            wx:for="{{ capturedImages }}" 
            wx:key="id"
            bindtap="previewImage"
            data-index="{{ index }}"
          >
            <image src="{{ item.url }}" mode="aspectFill"></image>
            <text class="capture-time">{{ item.time }}</text>
          </view>
        </scroll-view>
      </view>
      
      <!-- 聊天区域 -->
      <view class="chat-section">
        <view class="section-title">聊天记录</view>
        <view class="messages-container" id="message-container">
          <view 
            class="message-item {{ item.type }}" 
            wx:for="{{ messages }}" 
            wx:key="id"
          >
            <view class="message-content">
              <text>{{ item.content }}</text>
            </view>
            <view class="message-time">{{ item.time }}</view>
          </view>
        </view>
        <view class="message-input">
          <input 
            placeholder="输入消息..." 
            value="{{ newMessage }}" 
            bindinput="inputMessage"
            confirm-type="send"
            bindconfirm="sendMessage"
          />
          <view class="send-button" bindtap="sendMessage">发送</view>
        </view>
      </view>
    </view>
  </block>
</view>

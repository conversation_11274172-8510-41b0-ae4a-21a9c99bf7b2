/**app.wxss**/
page {
  --primary-color: #1890FF;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --font-size-small: 24rpx;
  --font-size-base: 28rpx;
  --font-size-medium: 32rpx;
  --font-size-large: 36rpx;
  --border-radius-small: 4rpx;
  --border-radius-base: 8rpx;
  --border-radius-large: 16rpx;
  
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: var(--font-size-base);
  color: #333;
  background-color: #f8f8f8;
}

.container {
  padding: 30rpx;
  box-sizing: border-box;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.margin-top {
  margin-top: 20rpx;
}

.margin-bottom {
  margin-bottom: 20rpx;
}

.margin-left {
  margin-left: 20rpx;
}

.margin-right {
  margin-right: 20rpx;
}

.padding {
  padding: 20rpx;
}

.card {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: var(--primary-color) !important;
  color: #fff !important;
  border: none !important;
}

.btn-block {
  width: 100% !important;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  margin-bottom: 10rpx;
  font-size: var(--font-size-medium);
  font-weight: 500;
}

.divider {
  height: 1px;
  background-color: #eee;
  margin: 30rpx 0;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  font-size: var(--font-size-small);
  border-radius: var(--border-radius-small);
  margin-right: 10rpx;
}

.tag-primary {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.tag-error {
  background-color: rgba(245, 34, 45, 0.1);
  color: var(--error-color);
}

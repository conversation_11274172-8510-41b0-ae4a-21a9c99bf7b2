/**app.wxss**/
page {
  --primary-color: #FF6B35;
  --primary-light: #FF8A65;
  --primary-dark: #E65100;
  --secondary-color: #FFB74D;
  --accent-color: #FF9800;
  --success-color: #4CAF50;
  --warning-color: #FFC107;
  --error-color: #F44336;
  --info-color: #2196F3;
  --font-size-small: 24rpx;
  --font-size-base: 28rpx;
  --font-size-medium: 32rpx;
  --font-size-large: 36rpx;
  --border-radius-small: 6rpx;
  --border-radius-base: 12rpx;
  --border-radius-large: 20rpx;
  --shadow-light: 0 2rpx 12rpx rgba(255, 107, 53, 0.1);
  --shadow-medium: 0 4rpx 20rpx rgba(255, 107, 53, 0.15);
  --shadow-heavy: 0 8rpx 30rpx rgba(255, 107, 53, 0.2);
  --gradient-primary: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  --gradient-secondary: linear-gradient(135deg, #FFB74D 0%, #FFC107 100%);

  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: var(--font-size-base);
  color: #333;
  background: linear-gradient(180deg, #FFF8F5 0%, #F5F5F5 100%);
}

.container {
  padding: 30rpx;
  box-sizing: border-box;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.margin-top {
  margin-top: 20rpx;
}

.margin-bottom {
  margin-bottom: 20rpx;
}

.margin-left {
  margin-left: 20rpx;
}

.margin-right {
  margin-right: 20rpx;
}

.padding {
  padding: 20rpx;
}

.card {
  background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%);
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow-light);
  border: 1px solid rgba(255, 107, 53, 0.08);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: var(--gradient-primary);
}

.btn-primary {
  background: var(--gradient-primary) !important;
  color: #fff !important;
  border: none !important;
  box-shadow: var(--shadow-medium) !important;
  transition: all 0.3s ease !important;
}

.btn-primary:active {
  transform: translateY(2rpx) !important;
  box-shadow: var(--shadow-light) !important;
}

.btn-block {
  width: 100% !important;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  margin-bottom: 10rpx;
  font-size: var(--font-size-medium);
  font-weight: 500;
}

.divider {
  height: 1px;
  background-color: #eee;
  margin: 30rpx 0;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  font-size: var(--font-size-small);
  border-radius: var(--border-radius-small);
  margin-right: 10rpx;
}

.tag-primary {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.tag-error {
  background-color: rgba(245, 34, 45, 0.1);
  color: var(--error-color);
}

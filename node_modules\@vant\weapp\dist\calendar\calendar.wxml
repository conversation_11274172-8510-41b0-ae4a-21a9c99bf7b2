<wxs src="./index.wxs" module="computed" />
<wxs src="../wxs/utils.wxs" module="utils" />
<view class="van-calendar">
  <header
    title="{{ title }}"
    showTitle="{{ showTitle }}"
    subtitle="{{ subtitle }}"
    showSubtitle="{{ showSubtitle }}"
    firstDayOfWeek="{{ firstDayOfWeek }}"
    bind:click-subtitle="onClickSubtitle"
  >
    <slot name="title" slot="title"></slot>
  </header>

  <scroll-view
    class="van-calendar__body"
    scroll-y
    scroll-into-view="{{ scrollIntoView }}"
  >
    <month
      wx:for="{{ computed.getMonths(minDate, maxDate) }}"
      wx:key="index"
      id="month{{ index }}"
      class="month"
      data-date="{{ item }}"
      date="{{ item }}"
      type="{{ type }}"
      color="{{ color }}"
      minDate="{{ minDate }}"
      maxDate="{{ maxDate }}"
      showMark="{{ showMark }}"
      formatter="{{ formatter }}"
      rowHeight="{{ rowHeight }}"
      currentDate="{{ currentDate }}"
      showSubtitle="{{ showSubtitle }}"
      allowSameDay="{{ allowSameDay }}"
      showMonthTitle="{{ index !== 0 || !showSubtitle }}"
      firstDayOfWeek="{{ firstDayOfWeek }}"
      bind:click="onClickDay"
    />
  </scroll-view>

  <view
    class="{{ utils.bem('calendar__footer', { safeAreaInsetBottom }) }}"
  >
    <slot name="footer"></slot>
  </view>

  <view
    class="{{ utils.bem('calendar__footer', { safeAreaInsetBottom }) }}"
  >
    <van-button
      wx:if="{{ showConfirm }}"
      round
      block
      type="danger"
      color="{{ color }}"
      custom-class="van-calendar__confirm"
      disabled="{{ computed.getButtonDisabled(type, currentDate, minRange) }}"
      nativeType="text"
      bind:click="onConfirm"
    >
      {{
        computed.getButtonDisabled(type, currentDate, minRange)
          ? confirmDisabledText
          : confirmText
      }}
    </van-button>
  </view>
</view>

// pages/user/user.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    isLogin: false,
    claimCount: 0,
    surveyCount: 0,
    lossCount: 0
  },

  onLoad: function () {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    } else if (this.data.canIUse) {
      // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
      // 所以此处加入 callback 以防止这种情况
      app.userInfoReadyCallback = res => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    } else {
      // 在没有 open-type=getUserInfo 版本的兼容处理
      wx.getUserInfo({
        success: res => {
          app.globalData.userInfo = res.userInfo
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          })
        }
      })
    }
    
    // 检查登录状态
    this.setData({
      isLogin: app.globalData.isLogin
    })
    
    // 获取统计数据
    this.getStatistics()
  },
  
  onShow: function () {
    // 每次显示页面时检查登录状态
    this.setData({
      isLogin: app.globalData.isLogin
    })
    
    // 刷新统计数据
    this.getStatistics()
  },
  
  getUserInfo: function(e) {
    console.log(e)
    app.globalData.userInfo = e.detail.userInfo
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },
  
  getStatistics: function () {
    // 模拟获取统计数据
    setTimeout(() => {
      this.setData({
        claimCount: 5,
        surveyCount: 2,
        lossCount: 3
      })
    }, 500)
  },
  
  navigateTo: function (e) {
    const url = e.currentTarget.dataset.url
    
    // 检查是否需要登录
    if (!this.data.isLogin && url !== '/pages/login/login') {
      wx.navigateTo({
        url: '/pages/login/login'
      })
      return
    }
    
    if (url.startsWith('/pages/')) {
      wx.navigateTo({
        url: url
      })
    } else {
      wx.switchTab({
        url: url
      })
    }
  },
  
  makePhoneCall: function () {
    wx.makePhoneCall({
      phoneNumber: '4001234567'
    })
  },
  
  logout: function () {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          app.globalData.isLogin = false
          app.globalData.token = ''
          
          this.setData({
            isLogin: false
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  }
})

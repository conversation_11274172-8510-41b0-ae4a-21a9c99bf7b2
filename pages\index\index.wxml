<!--pages/index/index.wxml-->
<view class="container">
  <!-- 顶部用户信息 -->
  <view class="user-info-bar">
    <view class="user-avatar">
      <image wx:if="{{hasUserInfo}}" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <view wx:else class="avatar-placeholder"></view>
    </view>
    <view class="user-greeting">
      <text wx:if="{{hasUserInfo}}">您好，{{userInfo.nickName}}</text>
      <text wx:else>您好，游客</text>
    </view>
    <view class="login-btn" wx:if="{{!hasUserInfo && canIUse}}">
      <button size="mini" open-type="getUserInfo" bindgetuserinfo="getUserInfo">登录</button>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper indicator-dots="true" autoplay="true" interval="3000" duration="500" circular="true">
      <block wx:for="{{banners}}" wx:key="id">
        <swiper-item>
          <image src="{{item.imageUrl}}" class="banner-image" mode="aspectFill" data-url="{{item.linkUrl}}" bindtap="navigateTo"></image>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 快捷功能区 -->
  <view class="quick-actions">
    <view class="quick-action-item" wx:for="{{quickActions}}" wx:key="id" data-url="{{item.url}}" bindtap="navigateTo">
      <image src="{{item.icon}}" class="quick-action-icon"></image>
      <text class="quick-action-name">{{item.name}}</text>
    </view>
  </view>

  <!-- 我的理赔 -->
  <view class="my-claims">
    <view class="section-title">
      <text>我的理赔</text>
      <text class="view-all" data-url="/pages/report/report" bindtap="navigateTo">查看全部 ></text>
    </view>

    <view class="claims-list">
      <view wx:if="{{loading}}" class="loading">
        <text>加载中...</text>
      </view>
      <block wx:elif="{{claimList.length > 0}}">
        <view class="claim-item" wx:for="{{claimList}}" wx:key="id" data-id="{{item.id}}" bindtap="viewClaimDetail">
          <view class="claim-header">
            <text class="claim-id">单号: {{item.id}}</text>
            <text class="claim-status status-{{item.status}}">{{app.globalData.claimStatus[item.status]}}</text>
          </view>
          <view class="claim-info">
            <view class="claim-type">
              <text class="label">险种:</text>
              <text class="value">{{item.type}}</text>
            </view>
            <view class="claim-date">
              <text class="label">报案日期:</text>
              <text class="value">{{item.date}}</text>
            </view>
          </view>
          <view class="claim-desc">
            <text class="label">事故描述:</text>
            <text class="value">{{item.description}}</text>
          </view>
        </view>
      </block>
      <view wx:else class="empty-claims">
        <image src="https://img.icons8.com/fluency/96/000000/no-data.png" mode="aspectFit" class="empty-image"></image>
        <text>暂无理赔记录</text>
        <button class="btn-primary" data-url="/pages/report/form" bindtap="navigateTo">立即报案</button>
      </view>
    </view>
  </view>

  <!-- 服务指南 -->
  <view class="service-guide">
    <view class="section-title">
      <text>服务指南</text>
    </view>
    <view class="guide-list">
      <view class="guide-item" data-url="/pages/guide/report" bindtap="navigateTo">
        <text class="guide-title">如何自助报案?</text>
        <text class="guide-arrow">></text>
      </view>
      <view class="guide-item" data-url="/pages/guide/survey" bindtap="navigateTo">
        <text class="guide-title">查勘流程说明</text>
        <text class="guide-arrow">></text>
      </view>
      <view class="guide-item" data-url="/pages/guide/loss" bindtap="navigateTo">
        <text class="guide-title">定损评估标准</text>
        <text class="guide-arrow">></text>
      </view>
    </view>
  </view>
</view>

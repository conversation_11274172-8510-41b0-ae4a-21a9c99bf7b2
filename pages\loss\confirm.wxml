<!--pages/loss/confirm.wxml-->
<view class="container">
  <view class="header">
    <text class="title">定损确认</text>
    <text class="claim-id">报案号：{{ claimId }}</text>
  </view>
  
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading color="#1890FF" size="24px">加载中...</van-loading>
  </view>
  
  <block wx:else>
    <view class="result-card">
      <view class="card-header">
        <view class="status-tag">已完成</view>
        <text class="loss-id">定损编号：{{ lossResult.id }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">定损时间</text>
        <text class="info-value">{{ lossResult.assessmentTime }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">定损方式</text>
        <text class="info-value">{{ lossResult.assessor }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">定损金额</text>
        <text class="info-value amount">¥{{ lossResult.totalAmount }}</text>
      </view>
    </view>
    
    <view class="result-card">
      <view class="card-title">定损明细</view>
      
      <view class="result-table">
        <view class="table-header">
          <view class="th th-name">项目名称</view>
          <view class="th th-price">单价</view>
          <view class="th th-quantity">数量</view>
          <view class="th th-amount">金额</view>
        </view>
        
        <view class="table-body">
          <view class="table-row" wx:for="{{ lossResult.items }}" wx:key="id">
            <view class="td td-name">
              <text>{{ item.name }}</text>
              <text class="td-remark">{{ item.remark }}</text>
            </view>
            <view class="td td-price">¥{{ item.price }}</view>
            <view class="td td-quantity">{{ item.quantity }}</view>
            <view class="td td-amount">¥{{ item.amount }}</view>
          </view>
        </view>
        
        <view class="table-footer">
          <text>合计金额</text>
          <text class="total-amount">¥{{ lossResult.totalAmount }}</text>
        </view>
      </view>
    </view>
    
    <view class="result-card">
      <view class="card-title">定损照片</view>
      <view class="images-container">
        <view 
          class="image-item" 
          wx:for="{{ lossResult.images }}" 
          wx:key="index"
          bindtap="previewImage"
          data-index="{{ index }}"
        >
          <image src="{{ item.url }}" mode="aspectFill"></image>
          <text class="image-name">{{ item.name }}</text>
        </view>
      </view>
    </view>
    
    <view class="result-card">
      <view class="card-title">备注说明</view>
      <view class="remarks">{{ lossResult.remarks }}</view>
    </view>
    
    <view class="signature-section">
      <view class="signature-title">电子签名确认</view>
      <view class="signature-container" bindtap="showSignature">
        <image wx:if="{{ signatureUrl }}" src="{{ signatureUrl }}" mode="aspectFit" class="signature-image"></image>
        <view wx:else class="signature-placeholder">
          <text>点击此处进行电子签名</text>
        </view>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="btn-primary" bindtap="confirmLoss" loading="{{ submitting }}" disabled="{{ submitting }}">
        {{ submitting ? '提交中...' : '确认定损结果' }}
      </button>
      <button class="btn-secondary" bindtap="showDispute">提出异议</button>
    </view>
  </block>
  
  <!-- 异议弹窗 -->
  <van-popup
    show="{{ showDisputePopup }}"
    position="bottom"
    round
    custom-style="height: 70%;"
    bind:close="onDisputeClose"
  >
    <view class="dispute-popup">
      <view class="popup-title">提出异议</view>
      
      <view class="dispute-form">
        <view class="form-item">
          <view class="form-label">异议原因</view>
          <view class="reason-list">
            <view 
              class="reason-item {{ disputeReason === '定损金额偏低' ? 'active' : '' }}" 
              bindtap="selectDisputeReason" 
              data-reason="定损金额偏低"
            >
              定损金额偏低
            </view>
            <view 
              class="reason-item {{ disputeReason === '漏项' ? 'active' : '' }}" 
              bindtap="selectDisputeReason" 
              data-reason="漏项"
            >
              漏项
            </view>
            <view 
              class="reason-item {{ disputeReason === '定损项目有误' ? 'active' : '' }}" 
              bindtap="selectDisputeReason" 
              data-reason="定损项目有误"
            >
              定损项目有误
            </view>
            <view 
              class="reason-item {{ disputeReason === '其他' ? 'active' : '' }}" 
              bindtap="selectDisputeReason" 
              data-reason="其他"
            >
              其他
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">异议内容</view>
          <textarea 
            placeholder="请详细描述您的异议内容" 
            value="{{ disputeContent }}" 
            bindinput="inputDisputeContent"
            maxlength="200"
          ></textarea>
          <view class="textarea-counter">{{ disputeContent.length }}/200</view>
        </view>
      </view>
      
      <view class="popup-buttons">
        <button class="popup-btn-cancel" bindtap="onDisputeClose">取消</button>
        <button 
          class="popup-btn-confirm" 
          bindtap="submitDispute" 
          loading="{{ submitting }}" 
          disabled="{{ submitting }}"
        >
          {{ submitting ? '提交中...' : '提交异议' }}
        </button>
      </view>
    </view>
  </van-popup>
  
  <!-- 签名弹窗 -->
  <van-popup
    show="{{ showSignaturePopup }}"
    position="bottom"
    round
    custom-style="height: 70%;"
    bind:close="onSignatureClose"
  >
    <view class="signature-popup">
      <view class="popup-title">电子签名</view>
      
      <view class="signature-canvas-container">
        <canvas 
          id="signature-canvas" 
          type="2d"
          class="signature-canvas"
          bindtouchstart="touchStart"
          bindtouchmove="touchMove"
          bindtouchend="touchEnd"
        ></canvas>
      </view>
      
      <view class="signature-tips">请在上方区域签下您的姓名</view>
      
      <view class="popup-buttons">
        <button class="popup-btn-cancel" bindtap="clearSignature">清除</button>
        <button class="popup-btn-confirm" bindtap="confirmSignature">确认签名</button>
      </view>
    </view>
  </van-popup>
</view>

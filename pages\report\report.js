// pages/report/report.js
const app = getApp()

Page({
  data: {
    activeTab: 0,
    claimList: [],
    loading: true,
    tabs: [
      { id: 0, name: '全部' },
      { id: 1, name: '处理中' },
      { id: 2, name: '已完成' }
    ]
  },

  onLoad: function (options) {
    this.loadClaimList()
  },
  
  onShow: function () {
    // 每次显示页面时刷新数据
    this.loadClaimList()
  },

  loadClaimList: function () {
    this.setData({
      loading: true
    })
    
    // 模拟获取理赔列表
    setTimeout(() => {
      const allClaims = [
        {
          id: 'CL20230001',
          type: '车险',
          status: 2,
          date: '2023-05-15',
          description: '交通事故车辆受损'
        },
        {
          id: 'CL20230002',
          type: '财产险',
          status: 4,
          date: '2023-05-10',
          description: '家庭财产水渍损失'
        },
        {
          id: 'CL20230003',
          type: '健康险',
          status: 6,
          date: '2023-04-20',
          description: '住院医疗费用报销'
        },
        {
          id: 'CL20230004',
          type: '意外险',
          status: 7,
          date: '2023-04-15',
          description: '运动伤害理赔'
        },
        {
          id: 'CL20230005',
          type: '车险',
          status: 3,
          date: '2023-05-18',
          description: '车辆划痕损失'
        }
      ]
      
      this.setData({
        claimList: allClaims,
        loading: false
      })
      
      this.filterClaimsByTab()
    }, 1000)
  },
  
  onTabChange: function (e) {
    const activeTab = e.detail.index
    this.setData({
      activeTab
    })
    
    this.filterClaimsByTab()
  },
  
  filterClaimsByTab: function () {
    const { activeTab, claimList } = this.data
    
    if (activeTab === 0) {
      // 全部
      this.setData({
        filteredClaimList: claimList
      })
    } else if (activeTab === 1) {
      // 处理中 (状态 1-5)
      this.setData({
        filteredClaimList: claimList.filter(claim => claim.status >= 1 && claim.status <= 5)
      })
    } else if (activeTab === 2) {
      // 已完成 (状态 6-7)
      this.setData({
        filteredClaimList: claimList.filter(claim => claim.status >= 6 && claim.status <= 7)
      })
    }
  },
  
  viewClaimDetail: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/report/detail?id=${id}`
    })
  },
  
  createNewClaim: function () {
    wx.navigateTo({
      url: '/pages/report/form'
    })
  },
  
  onPullDownRefresh: function () {
    this.loadClaimList()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})

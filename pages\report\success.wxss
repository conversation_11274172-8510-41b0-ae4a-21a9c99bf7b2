/* pages/report/success.wxss */
.container {
  min-height: 100vh;
  background-color: #fff;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  margin-bottom: 40rpx;
  background-color: rgba(82, 196, 26, 0.1);
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 60rpx;
}

.success-info {
  width: 100%;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 60rpx;
}

.info-item {
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: block;
}

.info-value-copy {
  display: flex;
  align-items: center;
}

.info-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.copy-btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  padding: 4rpx 16rpx;
  border: 1px solid var(--primary-color);
  border-radius: 20rpx;
}

.success-notice {
  width: 100%;
  background-color: #f6ffed;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 60rpx;
}

.notice-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #52c41a;
  margin-bottom: 20rpx;
}

.notice-content {
  
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.action-buttons {
  width: 100%;
}

.btn-primary, .btn-secondary {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

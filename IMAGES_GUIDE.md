# 图片资源使用指南

## 📸 当前图片解决方案

为了解决图片资源缺失的问题，我们采用了在线图片服务的方案，确保小程序能够正常运行和展示。

## 🌐 使用的在线图片服务

### 1. Picsum Photos (随机图片)
- **服务地址**: https://picsum.photos/
- **用途**: 轮播图、示例照片、占位图片
- **格式**: `https://picsum.photos/宽度/高度?random=随机数`
- **特点**: 提供高质量的随机图片，适合作为占位图片

### 2. Icons8 (图标服务)
- **服务地址**: https://img.icons8.com/
- **用途**: 功能图标、状态图标、装饰图标
- **格式**: `https://img.icons8.com/fluency/尺寸/颜色/图标名.png`
- **特点**: 提供统一风格的彩色图标

## 📋 图片使用清单

### 首页 (pages/index)
```javascript
// 轮播图
banners: [
  { imageUrl: 'https://picsum.photos/750/300?random=1' },
  { imageUrl: 'https://picsum.photos/750/300?random=2' },
  { imageUrl: 'https://picsum.photos/750/300?random=3' }
]

// 快捷功能图标
quickActions: [
  { icon: 'https://img.icons8.com/fluency/96/000000/file.png' },        // 自助报案
  { icon: 'https://img.icons8.com/fluency/96/000000/search.png' },      // 查勘预约
  { icon: 'https://img.icons8.com/fluency/96/000000/calculator.png' },  // 定损评估
  { icon: 'https://img.icons8.com/fluency/96/000000/progress-bar.png' } // 理赔进度
]

// 空状态图片
emptyImage: 'https://img.icons8.com/fluency/96/000000/no-data.png'
```

### 登录页 (pages/login)
```javascript
// 品牌Logo
logo: 'https://img.icons8.com/fluency/96/000000/insurance.png'

// 微信登录图标
wechatIcon: 'https://img.icons8.com/fluency/48/000000/wechat.png'
```

### 用户中心 (pages/user)
```javascript
// 功能图标
functionIcons: {
  report: 'https://img.icons8.com/fluency/48/000000/file.png',        // 自助报案
  survey: 'https://img.icons8.com/fluency/48/000000/search.png',      // 查勘预约
  loss: 'https://img.icons8.com/fluency/48/000000/calculator.png',    // 定损评估
  policy: 'https://img.icons8.com/fluency/48/000000/insurance.png',   // 保单管理
  addPolicy: 'https://img.icons8.com/fluency/48/000000/add-file.png', // 添加保单
  profile: 'https://img.icons8.com/fluency/48/000000/user.png',       // 个人资料
  about: 'https://img.icons8.com/fluency/48/000000/info.png',         // 关于我们
  service: 'https://img.icons8.com/fluency/48/000000/phone.png'       // 客服热线
}
```

### 报案模块 (pages/report)
```javascript
// 示例照片
sampleImages: [
  'https://picsum.photos/400/300?random=10',  // 车辆受损照片1
  'https://picsum.photos/400/300?random=11',  // 车辆受损照片2
  'https://picsum.photos/400/300?random=12'   // 事故现场
]

// 空状态图片
emptyImage: 'https://img.icons8.com/fluency/96/000000/no-data.png'
```

### 查勘模块 (pages/survey)
```javascript
// 远程查勘相关
remoteImages: {
  videoCall: 'https://img.icons8.com/fluency/96/000000/video-call.png',
  mainVideo: 'https://picsum.photos/750/500?random=60',
  selfVideo: 'https://picsum.photos/160/240?random=61',
  camera: 'https://img.icons8.com/fluency/96/000000/camera.png',
  endCall: 'https://img.icons8.com/fluency/96/000000/end-call.png',
  switchCamera: 'https://img.icons8.com/fluency/96/000000/switch-camera.png'
}

// 查勘结果照片
resultImages: [
  'https://picsum.photos/400/300?random=20',  // 车辆右前门受损
  'https://picsum.photos/400/300?random=21',  // 前保险杠刮擦
  'https://picsum.photos/400/300?random=22'   // 车牌照片
]

// 空状态图片
emptyImage: 'https://img.icons8.com/fluency/96/000000/no-data.png'
```

### 定损模块 (pages/loss)
```javascript
// 定损照片
assessmentImages: [
  'https://picsum.photos/400/300?random=30',  // 车辆受损照片1
  'https://picsum.photos/400/300?random=31',  // 车辆受损照片2
  'https://picsum.photos/400/300?random=32'   // 事故现场
]

// 定损确认照片
confirmImages: [
  'https://picsum.photos/400/300?random=40',  // 车辆右前门受损
  'https://picsum.photos/400/300?random=41'   // 前保险杠刮擦
]

// 电子签名
signature: 'https://picsum.photos/300/100?random=99'

// 空状态图片
emptyImage: 'https://img.icons8.com/fluency/96/000000/no-data.png'
```

## 🔄 替换为本地图片的步骤

如果您想使用自己的图片资源，请按照以下步骤操作：

### 1. 准备图片文件
在项目根目录创建 `images` 文件夹结构：
```
images/
├── tabbar/          # 底部导航图标
├── icons/           # 功能图标
├── banner/          # 轮播图
├── sample/          # 示例图片
└── logo/            # 品牌Logo
```

### 2. 添加图片文件
将您的图片文件放入对应的文件夹中，建议使用以下命名规范：
- **Logo**: `logo.png` (建议尺寸: 96x96px)
- **轮播图**: `banner1.png`, `banner2.png`, `banner3.png` (建议尺寸: 750x300px)
- **功能图标**: `report.png`, `survey.png`, `loss.png` 等 (建议尺寸: 48x48px)
- **示例照片**: `car_damage1.jpg`, `car_damage2.jpg` 等 (建议尺寸: 400x300px)

### 3. 更新代码中的图片路径
将在线图片URL替换为本地路径，例如：
```javascript
// 替换前
imageUrl: 'https://picsum.photos/750/300?random=1'

// 替换后
imageUrl: '/images/banner/banner1.png'
```

### 4. 图片优化建议
- **格式**: 优先使用 WebP 格式，其次是 PNG/JPG
- **尺寸**: 根据实际显示尺寸优化图片大小
- **压缩**: 使用图片压缩工具减小文件大小
- **命名**: 使用有意义的文件名，便于维护

## 📱 图片规范

### 尺寸建议
- **轮播图**: 750x300px (2.5:1)
- **功能图标**: 48x48px 或 96x96px (1:1)
- **示例照片**: 400x300px (4:3)
- **用户头像**: 80x80px (1:1)
- **Logo**: 96x96px (1:1)

### 格式要求
- **图标**: PNG (支持透明背景)
- **照片**: JPG/WebP (较小文件大小)
- **Logo**: PNG (支持透明背景)

### 文件大小
- **单个图片**: 建议不超过 200KB
- **总图片资源**: 建议不超过 2MB

## 🎨 设计建议

### 图标设计
- 使用统一的设计风格
- 保持简洁明了
- 适配橙色主题色彩
- 确保在小尺寸下清晰可见

### 照片选择
- 选择高质量、清晰的图片
- 避免包含敏感信息
- 确保图片内容与功能相关
- 考虑用户隐私保护

## 🔧 技术实现

### 图片懒加载
```javascript
// 实现图片懒加载
const lazyLoadImage = (imageSrc) => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(imageSrc)
    img.src = imageSrc
  })
}
```

### 图片缓存
```javascript
// 预加载重要图片
const preloadImages = (imageUrls) => {
  imageUrls.forEach(url => {
    const img = new Image()
    img.src = url
  })
}
```

### 错误处理
```javascript
// 图片加载失败处理
onImageError: function(e) {
  console.log('图片加载失败:', e.detail.errMsg)
  // 可以设置默认图片
  this.setData({
    imageUrl: '/images/default/placeholder.png'
  })
}
```

## 📝 注意事项

1. **网络依赖**: 当前使用在线图片服务，需要网络连接
2. **加载速度**: 在线图片可能影响加载速度
3. **稳定性**: 建议最终替换为本地图片资源
4. **版权问题**: 确保使用的图片符合版权要求
5. **小程序限制**: 注意小程序对图片资源的大小限制

---

通过使用在线图片服务，我们解决了图片资源缺失的问题，确保小程序能够正常运行。在实际部署时，建议替换为优化后的本地图片资源。

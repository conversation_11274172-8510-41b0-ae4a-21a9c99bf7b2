/**
 * API接口定义
 */

const { get, post, put, delete: del, upload } = require('../utils/request')

// 用户相关接口
const userApi = {
  // 登录
  login: (data) => post('/auth/login', data),
  
  // 获取验证码
  getVerifyCode: (phone) => post('/auth/verify-code', { phone }),
  
  // 获取用户信息
  getUserInfo: () => get('/user/info'),
  
  // 更新用户信息
  updateUserInfo: (data) => put('/user/info', data),
  
  // 退出登录
  logout: () => post('/auth/logout')
}

// 保单相关接口
const policyApi = {
  // 获取保单列表
  getPolicyList: (params) => get('/policies', params),
  
  // 获取保单详情
  getPolicyDetail: (id) => get(`/policies/${id}`),
  
  // 添加保单
  addPolicy: (data) => post('/policies', data),
  
  // 删除保单
  deletePolicy: (id) => del(`/policies/${id}`)
}

// 报案相关接口
const claimApi = {
  // 获取报案列表
  getClaimList: (params) => get('/claims', params),
  
  // 获取报案详情
  getClaimDetail: (id) => get(`/claims/${id}`),
  
  // 提交报案
  submitClaim: (data) => post('/claims', data),
  
  // 更新报案信息
  updateClaim: (id, data) => put(`/claims/${id}`, data),
  
  // 取消报案
  cancelClaim: (id) => del(`/claims/${id}`),
  
  // 上传报案资料
  uploadClaimFiles: (claimId, files) => {
    const uploadPromises = files.map(file => 
      upload('/claims/upload', file.path, { claimId, type: file.type })
    )
    return Promise.all(uploadPromises)
  }
}

// 查勘相关接口
const surveyApi = {
  // 获取查勘列表
  getSurveyList: (params) => get('/surveys', params),
  
  // 获取查勘详情
  getSurveyDetail: (id) => get(`/surveys/${id}`),
  
  // 预约查勘
  appointmentSurvey: (data) => post('/surveys/appointment', data),
  
  // 开始远程查勘
  startRemoteSurvey: (id) => post(`/surveys/${id}/remote/start`),
  
  // 结束远程查勘
  endRemoteSurvey: (id, data) => post(`/surveys/${id}/remote/end`, data),
  
  // 获取查勘结果
  getSurveyResult: (id) => get(`/surveys/${id}/result`),
  
  // 上传查勘资料
  uploadSurveyFiles: (surveyId, files) => {
    const uploadPromises = files.map(file => 
      upload('/surveys/upload', file.path, { surveyId, type: file.type })
    )
    return Promise.all(uploadPromises)
  }
}

// 定损相关接口
const lossApi = {
  // 获取定损列表
  getLossList: (params) => get('/losses', params),
  
  // 获取定损详情
  getLossDetail: (id) => get(`/losses/${id}`),
  
  // 开始定损评估
  startLossAssessment: (data) => post('/losses/assessment', data),
  
  // AI定损分析
  aiLossAnalysis: (images) => {
    const uploadPromises = images.map(image => 
      upload('/losses/ai-analysis', image.path, { type: 'analysis' })
    )
    return Promise.all(uploadPromises)
  },
  
  // 提交定损结果
  submitLossResult: (data) => post('/losses/result', data),
  
  // 确认定损结果
  confirmLossResult: (id, data) => post(`/losses/${id}/confirm`, data),
  
  // 提出定损异议
  submitLossDispute: (id, data) => post(`/losses/${id}/dispute`, data),
  
  // 上传定损资料
  uploadLossFiles: (lossId, files) => {
    const uploadPromises = files.map(file => 
      upload('/losses/upload', file.path, { lossId, type: file.type })
    )
    return Promise.all(uploadPromises)
  }
}

// 文件上传接口
const fileApi = {
  // 上传图片
  uploadImage: (filePath, type = 'image') => upload('/files/upload', filePath, { type }),
  
  // 上传视频
  uploadVideo: (filePath) => upload('/files/upload', filePath, { type: 'video' }),
  
  // 上传文档
  uploadDocument: (filePath) => upload('/files/upload', filePath, { type: 'document' }),
  
  // 删除文件
  deleteFile: (fileId) => del(`/files/${fileId}`)
}

// 消息相关接口
const messageApi = {
  // 获取消息列表
  getMessageList: (params) => get('/messages', params),
  
  // 获取消息详情
  getMessageDetail: (id) => get(`/messages/${id}`),
  
  // 标记消息已读
  markMessageRead: (id) => put(`/messages/${id}/read`),
  
  // 删除消息
  deleteMessage: (id) => del(`/messages/${id}`)
}

// 系统相关接口
const systemApi = {
  // 获取系统配置
  getSystemConfig: () => get('/system/config'),
  
  // 获取保险类型列表
  getInsuranceTypes: () => get('/system/insurance-types'),
  
  // 获取地区列表
  getRegionList: () => get('/system/regions'),
  
  // 意见反馈
  submitFeedback: (data) => post('/system/feedback', data),
  
  // 检查版本更新
  checkUpdate: () => get('/system/version'),
  
  // 获取客服信息
  getCustomerService: () => get('/system/customer-service')
}

// 统计相关接口
const statisticsApi = {
  // 获取用户统计数据
  getUserStatistics: () => get('/statistics/user'),
  
  // 获取理赔统计数据
  getClaimStatistics: (params) => get('/statistics/claims', params),
  
  // 获取查勘统计数据
  getSurveyStatistics: (params) => get('/statistics/surveys', params),
  
  // 获取定损统计数据
  getLossStatistics: (params) => get('/statistics/losses', params)
}

module.exports = {
  userApi,
  policyApi,
  claimApi,
  surveyApi,
  lossApi,
  fileApi,
  messageApi,
  systemApi,
  statisticsApi
}

<!--pages/survey/survey.wxml-->
<view class="container">
  <view class="header">
    <text class="title">查勘管理</text>
  </view>
  
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" swipeable animated color="#1890FF">
    <van-tab wx:for="{{ tabs }}" wx:key="id" title="{{ item.name }}">
      <view class="survey-list">
        <view wx:if="{{ loading }}" class="loading-container">
          <van-loading color="#1890FF" size="24px">加载中...</van-loading>
        </view>
        
        <block wx:elif="{{ filteredSurveyList.length > 0 }}">
          <view class="survey-item" wx:for="{{ filteredSurveyList }}" wx:key="id">
            <view class="survey-header" bindtap="viewSurveyDetail" data-id="{{ item.claimId }}">
              <text class="survey-id">单号: {{ item.claimId }}</text>
              <text class="survey-status status-{{ item.status }}">{{ item.status === 1 ? '待查勘' : '已完成' }}</text>
            </view>
            <view class="survey-info" bindtap="viewSurveyDetail" data-id="{{ item.claimId }}">
              <view class="survey-type">
                <text class="label">险种:</text>
                <text class="value">{{ item.type }}</text>
              </view>
              <view class="survey-date">
                <text class="label">报案日期:</text>
                <text class="value">{{ item.date }}</text>
              </view>
            </view>
            <view class="survey-desc" bindtap="viewSurveyDetail" data-id="{{ item.claimId }}">
              <text class="label">事故描述:</text>
              <text class="value">{{ item.description }}</text>
            </view>
            <view class="survey-appointment" bindtap="viewSurveyDetail" data-id="{{ item.claimId }}">
              <text class="label">预约时间:</text>
              <text class="value">{{ item.appointmentTime || '未预约' }}</text>
            </view>
            <view class="survey-actions" wx:if="{{ item.status === 1 }}">
              <button class="action-btn" bindtap="makeAppointment" data-id="{{ item.claimId }}">预约查勘</button>
              <button class="action-btn" bindtap="startRemoteSurvey" data-id="{{ item.claimId }}">远程查勘</button>
            </view>
            <view class="survey-actions" wx:else>
              <button class="action-btn" bindtap="viewSurveyResult" data-id="{{ item.claimId }}">查看结果</button>
            </view>
          </view>
        </block>
        
        <view wx:else class="empty-container">
          <image src="/images/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无查勘记录</text>
        </view>
      </view>
    </van-tab>
  </van-tabs>
</view>

<!--pages/report/form.wxml-->
<view class="container">
  <view class="header">
    <text class="title">自助报案</text>
  </view>
  
  <view class="steps-container">
    <van-steps steps="{{ steps }}" active="{{ currentStep }}" active-color="#1890FF" />
  </view>
  
  <!-- 步骤1：填写信息 -->
  <view class="step-content" wx:if="{{ currentStep === 0 }}">
    <view class="form-section">
      <view class="section-title">选择保单</view>
      <view class="policy-list">
        <view 
          class="policy-item {{ selectedPolicy && selectedPolicy.id === item.id ? 'active' : '' }}" 
          wx:for="{{ policyList }}" 
          wx:key="id"
          data-index="{{ index }}"
          bindtap="selectPolicy"
        >
          <view class="policy-info">
            <view class="policy-name">{{ item.name }}</view>
            <view class="policy-detail">
              <text class="policy-company">{{ item.company }}</text>
              <text class="policy-expire">有效期至: {{ item.expireDate }}</text>
            </view>
          </view>
          <view class="policy-check" wx:if="{{ selectedPolicy && selectedPolicy.id === item.id }}">
            <van-icon name="success" color="#1890FF" size="40rpx" />
          </view>
        </view>
      </view>
      <view class="error-message" wx:if="{{ errors.selectedPolicy }}">{{ errors.selectedPolicy }}</view>
    </view>
    
    <view class="form-section">
      <view class="section-title">出险信息</view>
      
      <view class="form-item">
        <view class="form-label">出险日期</view>
        <picker mode="date" value="{{ accidentDate }}" start="2020-01-01" end="2030-12-31" bindchange="bindDateChange">
          <view class="picker {{ accidentDate ? '' : 'placeholder' }}">
            {{ accidentDate || '请选择出险日期' }}
            <van-icon name="arrow" />
          </view>
        </picker>
        <view class="error-message" wx:if="{{ errors.accidentDate }}">{{ errors.accidentDate }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">出险时间</view>
        <picker mode="time" value="{{ accidentTime }}" bindchange="bindTimeChange">
          <view class="picker {{ accidentTime ? '' : 'placeholder' }}">
            {{ accidentTime || '请选择出险时间' }}
            <van-icon name="arrow" />
          </view>
        </picker>
        <view class="error-message" wx:if="{{ errors.accidentTime }}">{{ errors.accidentTime }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">出险地点</view>
        <view class="location-input">
          <input 
            placeholder="请输入出险地点" 
            value="{{ accidentLocation }}" 
            bindinput="inputLocation"
          />
          <view class="location-button" bindtap="useCurrentLocation">
            <van-icon name="location-o" size="32rpx" />
            <text>定位</text>
          </view>
        </view>
        <view class="error-message" wx:if="{{ errors.accidentLocation }}">{{ errors.accidentLocation }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">出险类型</view>
        <view class="picker {{ accidentType ? '' : 'placeholder' }}" bindtap="showInsuranceTypePicker">
          {{ accidentType || '请选择出险类型' }}
          <van-icon name="arrow" />
        </view>
        <van-popup
          show="{{ showInsuranceTypePicker }}"
          position="bottom"
          bind:close="onInsuranceTypePickerCancel"
        >
          <van-picker
            show-toolbar
            title="选择出险类型"
            columns="{{ insuranceTypes }}"
            value-key="name"
            bind:cancel="onInsuranceTypePickerCancel"
            bind:confirm="onInsuranceTypePickerConfirm"
          />
        </van-popup>
        <view class="error-message" wx:if="{{ errors.accidentType }}">{{ errors.accidentType }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">事故描述</view>
        <textarea 
          placeholder="请详细描述事故经过、损失情况等" 
          value="{{ accidentDescription }}" 
          bindinput="inputDescription"
          maxlength="500"
        ></textarea>
        <view class="textarea-counter">{{ accidentDescription.length }}/500</view>
        <view class="error-message" wx:if="{{ errors.accidentDescription }}">{{ errors.accidentDescription }}</view>
      </view>
    </view>
  </view>
  
  <!-- 步骤2：上传资料 -->
  <view class="step-content" wx:if="{{ currentStep === 1 }}">
    <view class="form-section">
      <view class="section-title">上传影像资料</view>
      <view class="upload-tips">
        <text>请上传清晰的事故现场照片、损失物品照片等，最多可上传9张</text>
      </view>
      
      <van-uploader
        file-list="{{ fileList }}"
        max-count="{{ maxFileCount }}"
        bind:after-read="afterRead"
        bind:delete="deleteFile"
        accept="image"
        multiple
      />
      
      <view class="error-message" wx:if="{{ errors.fileList }}">{{ errors.fileList }}</view>
      
      <view class="upload-guide">
        <view class="guide-title">拍摄指南：</view>
        <view class="guide-item">1. 车险：拍摄车辆受损部位、事故现场全景、车牌照片等</view>
        <view class="guide-item">2. 财产险：拍摄受损物品、受损范围全景等</view>
        <view class="guide-item">3. 健康险：拍摄医疗单据、诊断证明等</view>
        <view class="guide-item">4. 意外险：拍摄意外现场、伤情照片等</view>
      </view>
    </view>
  </view>
  
  <!-- 步骤3：提交确认 -->
  <view class="step-content" wx:if="{{ currentStep === 2 }}">
    <view class="form-section">
      <view class="section-title">报案信息确认</view>
      
      <view class="confirm-info">
        <view class="confirm-item">
          <text class="confirm-label">保单信息</text>
          <text class="confirm-value">{{ selectedPolicy.name }} ({{ selectedPolicy.company }})</text>
        </view>
        
        <view class="confirm-item">
          <text class="confirm-label">出险时间</text>
          <text class="confirm-value">{{ accidentDate }} {{ accidentTime }}</text>
        </view>
        
        <view class="confirm-item">
          <text class="confirm-label">出险地点</text>
          <text class="confirm-value">{{ accidentLocation }}</text>
        </view>
        
        <view class="confirm-item">
          <text class="confirm-label">出险类型</text>
          <text class="confirm-value">{{ accidentType }}</text>
        </view>
        
        <view class="confirm-item">
          <text class="confirm-label">事故描述</text>
          <text class="confirm-value">{{ accidentDescription }}</text>
        </view>
        
        <view class="confirm-item">
          <text class="confirm-label">上传资料</text>
          <text class="confirm-value">已上传 {{ fileList.length }} 张照片</text>
        </view>
      </view>
      
      <view class="confirm-notice">
        <text>提交后，我们将尽快处理您的报案申请，请保持电话畅通。</text>
      </view>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="footer-buttons">
    <block wx:if="{{ currentStep > 0 }}">
      <button class="btn-secondary" bindtap="prevStep">上一步</button>
    </block>
    
    <block wx:if="{{ currentStep < 2 }}">
      <button class="btn-primary" bindtap="nextStep">下一步</button>
    </block>
    
    <block wx:if="{{ currentStep === 2 }}">
      <button class="btn-primary" bindtap="submitClaim" loading="{{ submitting }}" disabled="{{ submitting }}">
        {{ submitting ? '提交中...' : '提交报案' }}
      </button>
    </block>
  </view>
</view>

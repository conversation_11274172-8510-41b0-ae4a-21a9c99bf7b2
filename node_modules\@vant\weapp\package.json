{"name": "@vant/weapp", "version": "1.11.7", "author": "vant-ui", "license": "MIT", "miniprogram": "lib", "description": "轻量、可靠的小程序 UI 组件库", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"dev": "node build/dev.mjs", "lint": "eslint ./packages --ext .js,.ts --fix && stylelint \"packages/**/*.less\" --fix", "prepare": "husky install", "release": "sh build/release.sh", "release:site": "vant-cli build-site && gh-pages -d site-dist --add", "build:lib": "yarn && npx gulp -f build/compiler.js --series buildEs buildLib", "upload:weapp": "node build/upload.js", "test": "jest", "test:watch": "jest --watch"}, "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+ssh://**************/youzan/vant-weapp.git"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{css,less}": ["stylelint --fix", "prettier --write"]}, "homepage": "https://github.com/youzan/vant-weapp#readme", "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@types/jest": "^27.0.2", "@vant/cli": "^7.0.3", "@vant/icons": "^3.0.1", "@vant/stylelint-config": "^1.4.2", "gulp": "^4.0.2", "gulp-insert": "^0.5.0", "gulp-less": "^5.0.0", "gulp-postcss": "^9.0.1", "gulp-rename": "^2.0.0", "gulp-typescript": "^6.0.0-alpha.1", "jest": "^27.3.1", "lint-staged": "^13.0.3", "merge2": "^1.4.1", "miniprogram-api-typings": "^3.1.6", "miniprogram-ci": "^1.6.1", "miniprogram-simulate": "^1.4.2", "stylelint": "13.13.1", "ts-jest": "^27.0.7", "tscpaths": "^0.0.9", "typescript": "^4.4.4", "vue": "^3.2.30"}, "browserslist": ["Chrome >= 53", "ChromeAndroid >= 53", "iOS >= 9"], "resolutions": {"source-map": "0.7.4"}}
/**
 * 网络请求封装
 */

const app = getApp()

// 请求基础配置
const config = {
  baseURL: 'https://api.claimease.com',
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}

/**
 * 请求拦截器
 * @param {Object} options 请求参数
 * @returns {Object} 处理后的请求参数
 */
const requestInterceptor = (options) => {
  // 添加基础URL
  if (!options.url.startsWith('http')) {
    options.url = config.baseURL + options.url
  }
  
  // 添加默认header
  options.header = {
    ...config.header,
    ...options.header
  }
  
  // 添加token
  if (app.globalData.token) {
    options.header.Authorization = `Bearer ${app.globalData.token}`
  }
  
  // 显示loading
  if (options.loading !== false) {
    wx.showLoading({
      title: options.loadingText || '请求中...',
      mask: true
    })
  }
  
  console.log('请求参数:', options)
  return options
}

/**
 * 响应拦截器
 * @param {Object} response 响应数据
 * @param {Object} options 请求参数
 * @returns {Promise} 处理后的响应数据
 */
const responseInterceptor = (response, options) => {
  // 隐藏loading
  if (options.loading !== false) {
    wx.hideLoading()
  }
  
  console.log('响应数据:', response)
  
  const { statusCode, data } = response
  
  // HTTP状态码检查
  if (statusCode >= 200 && statusCode < 300) {
    // 业务状态码检查
    if (data.code === 0 || data.success === true) {
      return Promise.resolve(data.data || data)
    } else {
      // 业务错误
      const errorMsg = data.message || data.msg || '请求失败'
      wx.showToast({
        title: errorMsg,
        icon: 'none'
      })
      return Promise.reject(new Error(errorMsg))
    }
  } else {
    // HTTP错误
    let errorMsg = '网络错误'
    
    switch (statusCode) {
      case 401:
        errorMsg = '未授权，请重新登录'
        // 清除token并跳转到登录页
        app.globalData.token = ''
        app.globalData.isLogin = false
        wx.navigateTo({
          url: '/pages/login/login'
        })
        break
      case 403:
        errorMsg = '拒绝访问'
        break
      case 404:
        errorMsg = '请求地址不存在'
        break
      case 500:
        errorMsg = '服务器内部错误'
        break
      case 502:
        errorMsg = '网关错误'
        break
      case 503:
        errorMsg = '服务不可用'
        break
      case 504:
        errorMsg = '网关超时'
        break
      default:
        errorMsg = `请求失败 ${statusCode}`
    }
    
    wx.showToast({
      title: errorMsg,
      icon: 'none'
    })
    
    return Promise.reject(new Error(errorMsg))
  }
}

/**
 * 错误处理
 * @param {Error} error 错误对象
 * @param {Object} options 请求参数
 */
const errorHandler = (error, options) => {
  // 隐藏loading
  if (options.loading !== false) {
    wx.hideLoading()
  }
  
  console.error('请求错误:', error)
  
  let errorMsg = '网络连接失败'
  
  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      errorMsg = '请求超时'
    } else if (error.errMsg.includes('fail')) {
      errorMsg = '网络连接失败'
    }
  }
  
  wx.showToast({
    title: errorMsg,
    icon: 'none'
  })
  
  return Promise.reject(error)
}

/**
 * 基础请求方法
 * @param {Object} options 请求参数
 * @returns {Promise} 请求结果
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const processedOptions = requestInterceptor(options)
    
    wx.request({
      ...processedOptions,
      timeout: config.timeout,
      success: (response) => {
        responseInterceptor(response, options)
          .then(resolve)
          .catch(reject)
      },
      fail: (error) => {
        errorHandler(error, options)
          .catch(reject)
      }
    })
  })
}

/**
 * GET请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

/**
 * POST请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 * @returns {Promise} 请求结果
 */
const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

/**
 * 文件上传
 * @param {string} url 上传地址
 * @param {string} filePath 文件路径
 * @param {Object} formData 额外的表单数据
 * @param {Object} options 其他选项
 * @returns {Promise} 上传结果
 */
const upload = (url, filePath, formData = {}, options = {}) => {
  return new Promise((resolve, reject) => {
    // 添加基础URL
    if (!url.startsWith('http')) {
      url = config.baseURL + url
    }
    
    // 添加token
    const header = { ...options.header }
    if (app.globalData.token) {
      header.Authorization = `Bearer ${app.globalData.token}`
    }
    
    // 显示loading
    if (options.loading !== false) {
      wx.showLoading({
        title: options.loadingText || '上传中...',
        mask: true
      })
    }
    
    wx.uploadFile({
      url,
      filePath,
      name: options.name || 'file',
      formData,
      header,
      success: (response) => {
        // 隐藏loading
        if (options.loading !== false) {
          wx.hideLoading()
        }
        
        try {
          const data = JSON.parse(response.data)
          if (data.code === 0 || data.success === true) {
            resolve(data.data || data)
          } else {
            const errorMsg = data.message || data.msg || '上传失败'
            wx.showToast({
              title: errorMsg,
              icon: 'none'
            })
            reject(new Error(errorMsg))
          }
        } catch (error) {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(error)
        }
      },
      fail: (error) => {
        // 隐藏loading
        if (options.loading !== false) {
          wx.hideLoading()
        }
        
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  upload
}

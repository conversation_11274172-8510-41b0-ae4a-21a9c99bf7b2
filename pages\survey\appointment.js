// pages/survey/appointment.js
Page({
  data: {
    claimId: '',
    claimDetail: null,
    loading: true,
    surveyType: 1, // 1-现场查勘, 2-远程查勘
    date: '',
    time: '',
    address: '',
    contactPerson: '',
    contactPhone: '',
    remarks: '',
    minDate: new Date().getTime(),
    maxDate: new Date().getTime() + 30 * 24 * 60 * 60 * 1000, // 30天后
    showDatePicker: false,
    showTimePicker: false,
    timeColumns: [
      { values: ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00'] }
    ],
    errors: {},
    submitting: false
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        claimId: options.id
      })
      
      this.loadClaimDetail(options.id)
    }
  },
  
  loadClaimDetail: function (id) {
    this.setData({
      loading: true
    })
    
    // 模拟获取理赔详情
    setTimeout(() => {
      // 模拟数据
      const claimDetail = {
        id: id,
        status: 2, // 查勘中
        policy: {
          id: 'P20230001',
          name: '车险-京A12345',
          company: '平安保险',
          expireDate: '2023-12-31'
        },
        accident: {
          date: '2023-05-15',
          time: '10:30',
          location: '北京市朝阳区三里屯太古里',
          type: '车险',
          description: '交通事故车辆受损，右前车门凹陷，前保险杠刮擦。'
        }
      }
      
      this.setData({
        claimDetail,
        loading: false,
        address: claimDetail.accident.location,
        contactPerson: '张三', // 模拟数据
        contactPhone: '13800138000' // 模拟数据
      })
    }, 1500)
  },
  
  onSurveyTypeChange: function (e) {
    this.setData({
      surveyType: e.detail
    })
  },
  
  showDatePicker: function () {
    this.setData({
      showDatePicker: true
    })
  },
  
  onDatePickerClose: function () {
    this.setData({
      showDatePicker: false
    })
  },
  
  onDatePickerConfirm: function (e) {
    const date = new Date(e.detail)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    
    this.setData({
      date: `${year}-${month}-${day}`,
      showDatePicker: false,
      'errors.date': ''
    })
  },
  
  showTimePicker: function () {
    this.setData({
      showTimePicker: true
    })
  },
  
  onTimePickerClose: function () {
    this.setData({
      showTimePicker: false
    })
  },
  
  onTimePickerConfirm: function (e) {
    this.setData({
      time: e.detail.value,
      showTimePicker: false,
      'errors.time': ''
    })
  },
  
  inputAddress: function (e) {
    this.setData({
      address: e.detail.value,
      'errors.address': ''
    })
  },
  
  inputContactPerson: function (e) {
    this.setData({
      contactPerson: e.detail.value,
      'errors.contactPerson': ''
    })
  },
  
  inputContactPhone: function (e) {
    this.setData({
      contactPhone: e.detail.value,
      'errors.contactPhone': ''
    })
  },
  
  inputRemarks: function (e) {
    this.setData({
      remarks: e.detail.value
    })
  },
  
  validateForm: function () {
    const { surveyType, date, time, address, contactPerson, contactPhone } = this.data
    let errors = {}
    let isValid = true
    
    if (!date) {
      errors.date = '请选择预约日期'
      isValid = false
    }
    
    if (!time) {
      errors.time = '请选择预约时间'
      isValid = false
    }
    
    if (surveyType === 1 && !address) {
      errors.address = '请填写查勘地址'
      isValid = false
    }
    
    if (!contactPerson) {
      errors.contactPerson = '请填写联系人'
      isValid = false
    }
    
    if (!contactPhone) {
      errors.contactPhone = '请填写联系电话'
      isValid = false
    } else if (!/^1\d{10}$/.test(contactPhone)) {
      errors.contactPhone = '联系电话格式不正确'
      isValid = false
    }
    
    this.setData({ errors })
    
    if (!isValid) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      })
    }
    
    return isValid
  },
  
  submitAppointment: function () {
    if (!this.validateForm()) {
      return
    }
    
    this.setData({
      submitting: true
    })
    
    // 模拟提交
    setTimeout(() => {
      this.setData({
        submitting: false
      })
      
      wx.showToast({
        title: '预约成功',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 2000)
  }
})

<!--pages/report/report.wxml-->
<view class="container">
  <view class="header">
    <text class="title">我的理赔</text>
  </view>
  
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" swipeable animated color="#1890FF">
    <van-tab wx:for="{{ tabs }}" wx:key="id" title="{{ item.name }}">
      <view class="claims-list">
        <view wx:if="{{ loading }}" class="loading-container">
          <van-loading color="#1890FF" size="24px">加载中...</van-loading>
        </view>
        
        <block wx:elif="{{ filteredClaimList.length > 0 }}">
          <view class="claim-item" wx:for="{{ filteredClaimList }}" wx:key="id" data-id="{{ item.id }}" bindtap="viewClaimDetail">
            <view class="claim-header">
              <text class="claim-id">单号: {{ item.id }}</text>
              <text class="claim-status status-{{ item.status }}">{{ app.globalData.claimStatus[item.status] }}</text>
            </view>
            <view class="claim-info">
              <view class="claim-type">
                <text class="label">险种:</text>
                <text class="value">{{ item.type }}</text>
              </view>
              <view class="claim-date">
                <text class="label">报案日期:</text>
                <text class="value">{{ item.date }}</text>
              </view>
            </view>
            <view class="claim-desc">
              <text class="label">事故描述:</text>
              <text class="value">{{ item.description }}</text>
            </view>
          </view>
        </block>
        
        <view wx:else class="empty-container">
          <image src="/images/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无理赔记录</text>
        </view>
      </view>
    </van-tab>
  </van-tabs>
  
  <view class="float-button" bindtap="createNewClaim">
    <text>+</text>
    <text class="button-text">新建报案</text>
  </view>
</view>

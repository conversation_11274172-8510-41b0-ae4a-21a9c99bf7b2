/* pages/loss/loss.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.loss-list {
  padding: 20rpx 30rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loss-item {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.loss-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.loss-id {
  font-size: 30rpx;
  font-weight: 500;
}

.loss-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status-1 {
  background-color: #f9f0ff;
  color: #722ed1;
}

.status-2 {
  background-color: #f6ffed;
  color: #52c41a;
}

.loss-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.loss-type, .loss-date {
  margin-right: 30rpx;
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.loss-desc, .loss-amount {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.label {
  color: #999;
  margin-right: 10rpx;
}

.value.amount {
  color: #f5222d;
  font-weight: 500;
}

.loss-actions {
  display: flex;
  margin-top: 20rpx;
  border-top: 1px solid #f0f0f0;
  padding-top: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  font-size: 26rpx;
  border-radius: 35rpx;
  background-color: var(--primary-color);
  color: #fff;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

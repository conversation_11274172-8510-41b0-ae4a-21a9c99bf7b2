<!--pages/survey/appointment.wxml-->
<view class="container">
  <view class="header">
    <text class="title">查勘预约</text>
  </view>
  
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading color="#1890FF" size="24px">加载中...</van-loading>
  </view>
  
  <block wx:else>
    <view class="claim-info">
      <view class="claim-id">报案号：{{ claimId }}</view>
      <view class="claim-type">险种：{{ claimDetail.accident.type }}</view>
      <view class="claim-desc">事故描述：{{ claimDetail.accident.description }}</view>
    </view>
    
    <view class="form-section">
      <view class="form-item">
        <view class="form-label">查勘方式</view>
        <van-radio-group value="{{ surveyType }}" bind:change="onSurveyTypeChange" direction="horizontal">
          <van-radio name="1">现场查勘</van-radio>
          <van-radio name="2">远程查勘</van-radio>
        </van-radio-group>
      </view>
      
      <view class="form-item">
        <view class="form-label">预约日期</view>
        <view class="picker {{ date ? '' : 'placeholder' }}" bindtap="showDatePicker">
          {{ date || '请选择预约日期' }}
          <van-icon name="arrow" />
        </view>
        <view class="error-message" wx:if="{{ errors.date }}">{{ errors.date }}</view>
        
        <van-popup
          show="{{ showDatePicker }}"
          position="bottom"
          bind:close="onDatePickerClose"
        >
          <van-datetime-picker
            type="date"
            value="{{ minDate }}"
            min-date="{{ minDate }}"
            max-date="{{ maxDate }}"
            bind:confirm="onDatePickerConfirm"
            bind:cancel="onDatePickerClose"
          />
        </van-popup>
      </view>
      
      <view class="form-item">
        <view class="form-label">预约时间</view>
        <view class="picker {{ time ? '' : 'placeholder' }}" bindtap="showTimePicker">
          {{ time || '请选择预约时间' }}
          <van-icon name="arrow" />
        </view>
        <view class="error-message" wx:if="{{ errors.time }}">{{ errors.time }}</view>
        
        <van-popup
          show="{{ showTimePicker }}"
          position="bottom"
          bind:close="onTimePickerClose"
        >
          <van-picker
            show-toolbar
            title="选择时间"
            columns="{{ timeColumns }}"
            bind:confirm="onTimePickerConfirm"
            bind:cancel="onTimePickerClose"
          />
        </van-popup>
      </view>
      
      <view class="form-item" wx:if="{{ surveyType == 1 }}">
        <view class="form-label">查勘地址</view>
        <input 
          placeholder="请输入查勘地址" 
          value="{{ address }}" 
          bindinput="inputAddress"
        />
        <view class="error-message" wx:if="{{ errors.address }}">{{ errors.address }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">联系人</view>
        <input 
          placeholder="请输入联系人姓名" 
          value="{{ contactPerson }}" 
          bindinput="inputContactPerson"
        />
        <view class="error-message" wx:if="{{ errors.contactPerson }}">{{ errors.contactPerson }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">联系电话</view>
        <input 
          type="number"
          placeholder="请输入联系电话" 
          value="{{ contactPhone }}" 
          bindinput="inputContactPhone"
          maxlength="11"
        />
        <view class="error-message" wx:if="{{ errors.contactPhone }}">{{ errors.contactPhone }}</view>
      </view>
      
      <view class="form-item">
        <view class="form-label">备注说明</view>
        <textarea 
          placeholder="请输入备注说明（选填）" 
          value="{{ remarks }}" 
          bindinput="inputRemarks"
          maxlength="200"
        ></textarea>
        <view class="textarea-counter">{{ remarks.length }}/200</view>
      </view>
    </view>
    
    <view class="notice-section">
      <view class="notice-title">预约须知：</view>
      <view class="notice-item">1. 请确保预约时间在工作日的9:00-17:00之间</view>
      <view class="notice-item">2. 现场查勘需确保您或委托人在约定时间到场</view>
      <view class="notice-item">3. 远程查勘需确保网络畅通，准备好相关资料</view>
      <view class="notice-item">4. 如需变更预约，请提前2小时联系客服</view>
    </view>
    
    <view class="submit-section">
      <button class="submit-button" bindtap="submitAppointment" loading="{{ submitting }}" disabled="{{ submitting }}">
        {{ submitting ? '提交中...' : '确认预约' }}
      </button>
    </view>
  </block>
</view>

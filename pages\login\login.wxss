/* pages/login/login.wxss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 60rpx 40rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #999;
}

.login-form {
  flex: 1;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.input-container {
  display: flex;
  align-items: center;
  height: 90rpx;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 10rpx;
}

.prefix {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

input {
  flex: 1;
  height: 100%;
  font-size: 32rpx;
}

.verification-code {
  position: relative;
}

.code-button {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: var(--primary-color);
  padding: 10rpx 0 10rpx 20rpx;
  border-left: 1px solid #e0e0e0;
}

.code-button.disabled {
  color: #999;
}

.agreement {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 1px solid #e0e0e0;
  border-radius: 4rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-inner {
  width: 24rpx;
  height: 24rpx;
  background-color: transparent;
  border-radius: 2rpx;
}

.checkbox-inner.checked {
  background-color: var(--primary-color);
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
}

.link {
  color: var(--primary-color);
}

.login-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 60rpx;
}

.other-login {
  margin-top: 80rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.line {
  flex: 1;
  height: 1px;
  background-color: #e0e0e0;
}

.divider text {
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #999;
}

.login-methods {
  display: flex;
  justify-content: center;
}

.login-method {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-method image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.login-method text {
  font-size: 26rpx;
  color: #666;
}

.login-footer {
  margin-top: 60rpx;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

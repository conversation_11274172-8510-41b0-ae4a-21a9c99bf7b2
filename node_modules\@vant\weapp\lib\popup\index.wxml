<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./index.wxs" module="computed" />

<import src="./popup.wxml" />

<van-overlay
  wx:if="{{ overlay }}"
  show="{{ show }}"
  z-index="{{ zIndex }}"
  custom-style="{{ overlayStyle }}"
  duration="{{ duration }}"
  bind:click="onClickOverlay"
  lock-scroll="{{ lockScroll }}"
  root-portal="{{ rootPortal }}"
/>

<root-portal wx:if="{{ rootPortal }}">
  <include src="./popup.wxml" />
</root-portal>

 <include wx:else src="./popup.wxml" />

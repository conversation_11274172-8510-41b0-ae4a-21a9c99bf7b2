// pages/survey/survey.js
const app = getApp()

Page({
  data: {
    activeTab: 0,
    loading: true,
    surveyList: [],
    tabs: [
      { id: 0, name: '全部' },
      { id: 1, name: '待查勘' },
      { id: 2, name: '已完成' }
    ]
  },

  onLoad: function (options) {
    this.loadSurveyList()
  },
  
  onShow: function () {
    // 每次显示页面时刷新数据
    this.loadSurveyList()
  },

  loadSurveyList: function () {
    this.setData({
      loading: true
    })
    
    // 模拟获取查勘列表
    setTimeout(() => {
      const allSurveys = [
        {
          id: 'CL20230001',
          claimId: 'CL20230001',
          type: '车险',
          status: 1, // 1-待查勘, 2-已完成
          date: '2023-05-15',
          appointmentTime: '2023-05-17 14:30',
          surveyType: 1, // 1-现场查勘, 2-远程查勘
          description: '交通事故车辆受损'
        },
        {
          id: 'CL20230002',
          claimId: 'CL20230002',
          type: '财产险',
          status: 1,
          date: '2023-05-10',
          appointmentTime: '2023-05-18 10:00',
          surveyType: 2,
          description: '家庭财产水渍损失'
        },
        {
          id: 'CL20230003',
          claimId: 'CL20230003',
          type: '健康险',
          status: 2,
          date: '2023-04-20',
          appointmentTime: '2023-04-22 15:00',
          surveyType: 2,
          description: '住院医疗费用报销'
        }
      ]
      
      this.setData({
        surveyList: allSurveys,
        loading: false
      })
      
      this.filterSurveysByTab()
    }, 1000)
  },
  
  onTabChange: function (e) {
    const activeTab = e.detail.index
    this.setData({
      activeTab
    })
    
    this.filterSurveysByTab()
  },
  
  filterSurveysByTab: function () {
    const { activeTab, surveyList } = this.data
    
    if (activeTab === 0) {
      // 全部
      this.setData({
        filteredSurveyList: surveyList
      })
    } else if (activeTab === 1) {
      // 待查勘
      this.setData({
        filteredSurveyList: surveyList.filter(survey => survey.status === 1)
      })
    } else if (activeTab === 2) {
      // 已完成
      this.setData({
        filteredSurveyList: surveyList.filter(survey => survey.status === 2)
      })
    }
  },
  
  viewSurveyDetail: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/report/detail?id=${id}`
    })
  },
  
  makeAppointment: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/survey/appointment?id=${id}`
    })
  },
  
  startRemoteSurvey: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/survey/remote?id=${id}`
    })
  },
  
  viewSurveyResult: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/survey/result?id=${id}`
    })
  },
  
  onPullDownRefresh: function () {
    this.loadSurveyList()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})

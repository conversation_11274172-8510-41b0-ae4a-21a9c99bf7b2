/* pages/survey/survey.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.survey-list {
  padding: 20rpx 30rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.survey-item {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.survey-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.survey-id {
  font-size: 30rpx;
  font-weight: 500;
}

.survey-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status-1 {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-2 {
  background-color: #f6ffed;
  color: #52c41a;
}

.survey-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.survey-type, .survey-date {
  margin-right: 30rpx;
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.survey-desc, .survey-appointment {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.label {
  color: #999;
  margin-right: 10rpx;
}

.survey-actions {
  display: flex;
  margin-top: 20rpx;
  border-top: 1px solid #f0f0f0;
  padding-top: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  font-size: 26rpx;
  border-radius: 35rpx;
  margin: 0 10rpx;
  background-color: #f5f5f5;
  color: #666;
}

.action-btn:first-child {
  margin-left: 0;
  background-color: var(--primary-color);
  color: #fff;
}

.action-btn:last-child {
  margin-right: 0;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

<!--pages/report/detail.wxml-->
<view class="container">
  <view class="header">
    <view class="claim-id">
      <text>报案号：{{ claimId }}</text>
      <view class="copy-btn" bindtap="copyClaimId">复制</view>
    </view>
    <view class="claim-status" wx:if="{{ !loading }}">
      <text class="status-text status-{{ claimDetail.status }}">{{ app.globalData.claimStatus[claimDetail.status] }}</text>
    </view>
  </view>
  
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading color="#1890FF" size="24px">加载中...</van-loading>
  </view>
  
  <block wx:else>
    <!-- 理赔进度 -->
    <view class="progress-section">
      <view class="section-title">理赔进度</view>
      <van-steps steps="{{ progressSteps }}" active="{{ activeStep }}" direction="horizontal" active-color="#1890FF" />
    </view>
    
    <!-- 详细信息 -->
    <van-collapse value="{{ activeNames }}" bind:change="onCollapseChange">
      <!-- 保单信息 -->
      <van-collapse-item title="保单信息" name="1">
        <view class="info-item">
          <text class="info-label">保单名称</text>
          <text class="info-value">{{ claimDetail.policy.name }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">保险公司</text>
          <text class="info-value">{{ claimDetail.policy.company }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">有效期至</text>
          <text class="info-value">{{ claimDetail.policy.expireDate }}</text>
        </view>
      </van-collapse-item>
      
      <!-- 出险信息 -->
      <van-collapse-item title="出险信息" name="2">
        <view class="info-item">
          <text class="info-label">出险时间</text>
          <text class="info-value">{{ claimDetail.accident.date }} {{ claimDetail.accident.time }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">出险地点</text>
          <text class="info-value">{{ claimDetail.accident.location }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">出险类型</text>
          <text class="info-value">{{ claimDetail.accident.type }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">事故描述</text>
          <text class="info-value">{{ claimDetail.accident.description }}</text>
        </view>
      </van-collapse-item>
      
      <!-- 影像资料 -->
      <van-collapse-item title="影像资料" name="3">
        <view class="images-container">
          <view class="image-item" wx:for="{{ claimDetail.images }}" wx:key="index" bindtap="previewImage" data-index="{{ index }}">
            <image src="{{ item.url }}" mode="aspectFill"></image>
            <text class="image-name">{{ item.name }}</text>
          </view>
        </view>
      </van-collapse-item>
    </van-collapse>
    
    <!-- 查勘信息 -->
    <view class="action-section">
      <view class="section-title">查勘信息</view>
      <view class="action-content">
        <block wx:if="{{ claimDetail.survey.status === 0 }}">
          <view class="status-message">查勘尚未开始</view>
        </block>
        
        <block wx:elif="{{ claimDetail.survey.status === 1 }}">
          <view class="status-message">请选择查勘方式</view>
          <view class="action-buttons">
            <button class="btn-primary" bindtap="makeAppointment">预约查勘</button>
            <button class="btn-secondary" bindtap="startRemoteSurvey">远程查勘</button>
          </view>
        </block>
        
        <block wx:elif="{{ claimDetail.survey.status === 2 }}">
          <view class="status-message">
            <text>已预约查勘时间：{{ claimDetail.survey.appointmentTime }}</text>
            <text>查勘员：{{ claimDetail.survey.surveyor }}</text>
          </view>
        </block>
        
        <block wx:elif="{{ claimDetail.survey.status === 3 }}">
          <view class="status-message">查勘已完成</view>
          <view class="action-buttons">
            <button class="btn-primary" bindtap="viewSurveyResult">查看查勘结果</button>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 定损信息 -->
    <view class="action-section">
      <view class="section-title">定损信息</view>
      <view class="action-content">
        <block wx:if="{{ claimDetail.loss.status === 0 }}">
          <view class="status-message">定损尚未开始</view>
          <view class="action-buttons" wx:if="{{ claimDetail.survey.status === 3 }}">
            <button class="btn-primary" bindtap="startLossAssessment">开始定损评估</button>
          </view>
        </block>
        
        <block wx:elif="{{ claimDetail.loss.status === 1 }}">
          <view class="status-message">定损评估中</view>
        </block>
        
        <block wx:elif="{{ claimDetail.loss.status === 2 }}">
          <view class="status-message">
            <text>定损已完成</text>
            <text>定损金额：¥{{ claimDetail.loss.amount }}</text>
          </view>
          <view class="action-buttons">
            <button class="btn-primary" bindtap="viewLossResult">查看定损明细</button>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 理赔记录 -->
    <view class="timeline-section">
      <view class="section-title">理赔记录</view>
      <view class="timeline">
        <view class="timeline-item" wx:for="{{ claimDetail.timeline }}" wx:key="index">
          <view class="timeline-time">{{ item.time }}</view>
          <view class="timeline-content">
            <view class="timeline-title">{{ item.title }}</view>
            <view class="timeline-desc">{{ item.content }}</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 联系客服 -->
    <view class="contact-section">
      <button class="contact-button" bindtap="contactCustomerService">
        <van-icon name="service-o" size="40rpx" />
        <text>联系客服</text>
      </button>
    </view>
  </block>
</view>

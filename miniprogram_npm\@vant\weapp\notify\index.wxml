<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./index.wxs" module="computed" />

<van-transition
  name="slide-down"
  show="{{ show }}"
  custom-class="van-notify__container"
  custom-style="{{ computed.rootStyle({ zIndex, top }) }}"
  bind:tap="onTap"
>
  <view
    class="van-notify van-notify--{{ type }}"
    style="{{ computed.notifyStyle({ background, color }) }}"
  >
    <view
      wx:if="{{ safeAreaInsetTop }}"
      style="height: {{ statusBarHeight }}px"
    />
    <text>{{ message }}</text>
  </view>
</van-transition>

/* pages/report/form.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.steps-container {
  padding: 30rpx 0;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.step-content {
  padding: 0 30rpx;
}

.form-section {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
  color: #333;
}

/* 保单列表 */
.policy-list {
  
}

.policy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius-base);
  margin-bottom: 20rpx;
}

.policy-item.active {
  border-color: var(--primary-color);
  background-color: rgba(24, 144, 255, 0.05);
}

.policy-info {
  flex: 1;
}

.policy-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.policy-detail {
  font-size: 26rpx;
  color: #666;
}

.policy-company {
  margin-right: 20rpx;
}

.policy-check {
  margin-left: 20rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.placeholder {
  color: #999;
}

.location-input {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  padding: 0 20rpx;
  height: 80rpx;
}

.location-input input {
  flex: 1;
  height: 100%;
}

.location-button {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  font-size: 26rpx;
  padding-left: 20rpx;
  border-left: 1px solid #e0e0e0;
}

.location-button text {
  margin-left: 6rpx;
}

textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  box-sizing: border-box;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.error-message {
  color: var(--error-color);
  font-size: 24rpx;
  margin-top: 10rpx;
}

/* 上传区域 */
.upload-tips {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.upload-guide {
  margin-top: 30rpx;
  font-size: 26rpx;
  color: #666;
}

.guide-title {
  margin-bottom: 10rpx;
  color: #333;
}

.guide-item {
  margin-bottom: 10rpx;
}

/* 确认信息 */
.confirm-info {
  
}

.confirm-item {
  margin-bottom: 20rpx;
}

.confirm-label {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.confirm-value {
  font-size: 30rpx;
  color: #333;
}

.confirm-notice {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f6ffed;
  border-radius: var(--border-radius-base);
  font-size: 26rpx;
  color: #52c41a;
}

/* 底部按钮 */
.footer-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.btn-primary, .btn-secondary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
  margin-left: 20rpx;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

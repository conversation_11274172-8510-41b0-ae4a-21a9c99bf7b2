<!--pages/user/user.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="user-header">
    <view class="user-info">
      <view class="avatar-container">
        <image wx:if="{{ hasUserInfo }}" src="{{ userInfo.avatarUrl }}" mode="cover" class="avatar"></image>
        <view wx:else class="avatar-placeholder"></view>
      </view>

      <view class="user-detail">
        <block wx:if="{{ isLogin }}">
          <text class="username">{{ hasUserInfo ? userInfo.nickName : '用户' }}</text>
          <text class="user-id">ID: 1234567890</text>
        </block>
        <block wx:else>
          <text class="username">未登录</text>
          <text class="login-tip">登录后享受更多服务</text>
        </block>
      </view>
    </view>

    <view class="login-button" wx:if="{{ !isLogin }}">
      <button bindtap="navigateTo" data-url="/pages/login/login">登录/注册</button>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="statistics">
    <view class="stat-item" bindtap="navigateTo" data-url="/pages/report/report">
      <text class="stat-number">{{ claimCount }}</text>
      <text class="stat-label">我的报案</text>
    </view>
    <view class="stat-item" bindtap="navigateTo" data-url="/pages/survey/survey">
      <text class="stat-number">{{ surveyCount }}</text>
      <text class="stat-label">我的查勘</text>
    </view>
    <view class="stat-item" bindtap="navigateTo" data-url="/pages/loss/loss">
      <text class="stat-number">{{ lossCount }}</text>
      <text class="stat-label">我的定损</text>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="function-list">
    <view class="function-group">
      <view class="function-title">我的服务</view>

      <view class="function-item" bindtap="navigateTo" data-url="/pages/report/form">
        <image src="https://img.icons8.com/fluency/48/000000/file.png" class="function-icon"></image>
        <text class="function-name">自助报案</text>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>

      <view class="function-item" bindtap="navigateTo" data-url="/pages/survey/appointment">
        <image src="https://img.icons8.com/fluency/48/000000/search.png" class="function-icon"></image>
        <text class="function-name">查勘预约</text>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>

      <view class="function-item" bindtap="navigateTo" data-url="/pages/loss/assessment">
        <image src="https://img.icons8.com/fluency/48/000000/calculator.png" class="function-icon"></image>
        <text class="function-name">定损评估</text>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
    </view>

    <view class="function-group">
      <view class="function-title">我的保单</view>

      <view class="function-item" bindtap="navigateTo" data-url="/pages/policy/list">
        <image src="https://img.icons8.com/fluency/48/000000/insurance.png" class="function-icon"></image>
        <text class="function-name">保单管理</text>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>

      <view class="function-item" bindtap="navigateTo" data-url="/pages/policy/add">
        <image src="https://img.icons8.com/fluency/48/000000/add-file.png" class="function-icon"></image>
        <text class="function-name">添加保单</text>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
    </view>

    <view class="function-group">
      <view class="function-title">设置</view>

      <view class="function-item" bindtap="navigateTo" data-url="/pages/settings/profile">
        <image src="https://img.icons8.com/fluency/48/000000/user.png" class="function-icon"></image>
        <text class="function-name">个人资料</text>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>

      <view class="function-item" bindtap="navigateTo" data-url="/pages/settings/about">
        <image src="https://img.icons8.com/fluency/48/000000/info.png" class="function-icon"></image>
        <text class="function-name">关于我们</text>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>

      <view class="function-item" bindtap="makePhoneCall">
        <image src="https://img.icons8.com/fluency/48/000000/phone.png" class="function-icon"></image>
        <text class="function-name">客服热线</text>
        <view class="function-value">************</view>
        <view class="function-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{ isLogin }}">
    <button class="logout-button" bindtap="logout">退出登录</button>
  </view>

  <view class="footer">
    <text>© 2023 ClaimEase 保险理赔助手</text>
  </view>
</view>

// pages/survey/result.js
Page({
  data: {
    claimId: '',
    loading: true,
    surveyResult: null
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        claimId: options.id
      })
      
      this.loadSurveyResult(options.id)
    }
  },
  
  loadSurveyResult: function (id) {
    this.setData({
      loading: true
    })
    
    // 模拟获取查勘结果
    setTimeout(() => {
      // 模拟数据
      const surveyResult = {
        id: 'SR' + id.substring(2),
        claimId: id,
        surveyType: '远程查勘',
        surveyTime: '2023-05-17 14:30',
        surveyor: '李工',
        surveyorPhone: '13900139000',
        status: 'completed',
        conclusion: '经查勘确认，车辆右前车门凹陷，前保险杠刮擦，符合报案描述的出险情况。损失程度中等，建议进行定损评估。',
        images: [
          { url: '/images/sample/car_damage1.jpg', name: '车辆右前门受损' },
          { url: '/images/sample/car_damage2.jpg', name: '前保险杠刮擦' },
          { url: '/images/sample/car_plate.jpg', name: '车牌照片' }
        ],
        nextStep: '定损评估',
        remarks: '请尽快安排定损评估，以便加快理赔进度。'
      }
      
      this.setData({
        surveyResult,
        loading: false
      })
    }, 1500)
  },
  
  previewImage: function (e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.surveyResult.images.map(img => img.url)
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },
  
  startLossAssessment: function () {
    wx.navigateTo({
      url: `/pages/loss/assessment?id=${this.data.claimId}`
    })
  },
  
  contactSurveyor: function () {
    const phone = this.data.surveyResult.surveyorPhone
    
    wx.showModal({
      title: '联系查勘员',
      content: `确定要拨打查勘员电话 ${phone} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phone
          })
        }
      }
    })
  },
  
  backToDetail: function () {
    wx.navigateBack()
  }
})

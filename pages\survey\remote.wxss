/* pages/survey/remote.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.claim-id {
  font-size: 28rpx;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

/* 未连接状态 */
.connect-section {
  padding: 60rpx 30rpx;
}

.connect-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.connect-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.connect-title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.connect-desc {
  font-size: 28rpx;
  color: #666;
}

.connect-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.guide-section {
  margin-top: 60rpx;
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.guide-title {
  font-size: 32rpx;
  font-weight: 500;
}

.guide-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.guide-content {
  
}

.guide-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.guide-number {
  width: 40rpx;
  height: 40rpx;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.guide-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

/* 已连接状态 */
.call-section {
  
}

.video-container {
  position: relative;
  width: 100%;
  height: 500rpx;
  background-color: #000;
  margin-bottom: 20rpx;
}

.main-video {
  width: 100%;
  height: 100%;
}

.self-video-container {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 160rpx;
  height: 240rpx;
  border: 2px solid #fff;
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.self-video {
  width: 100%;
  height: 100%;
}

.call-info {
  position: absolute;
  left: 20rpx;
  top: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.call-duration {
  color: #fff;
  font-size: 24rpx;
}

.call-controls {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 20rpx;
  display: flex;
  justify-content: center;
}

.control-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 30rpx;
}

.control-button image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.control-button text {
  color: #fff;
  font-size: 24rpx;
}

.control-end image {
  background-color: #f5222d;
  border-radius: 50%;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.captures-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.captures-list {
  padding: 20rpx 30rpx;
  white-space: nowrap;
}

.capture-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  position: relative;
}

.capture-item image {
  width: 100%;
  height: 150rpx;
  border-radius: var(--border-radius-base);
}

.capture-time {
  position: absolute;
  right: 10rpx;
  bottom: 10rpx;
  font-size: 20rpx;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
}

.chat-section {
  background-color: #fff;
}

.messages-container {
  padding: 20rpx 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.message-item {
  margin-bottom: 20rpx;
}

.message-item.self {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  word-break: break-all;
}

.self .message-content {
  background-color: var(--primary-color);
  color: #fff;
  border-top-right-radius: 4rpx;
}

.other .message-content {
  background-color: #f0f0f0;
  color: #333;
  border-top-left-radius: 4rpx;
}

.system .message-content {
  background-color: #f6ffed;
  color: #52c41a;
  text-align: center;
  margin: 0 auto;
}

.message-time {
  font-size: 22rpx;
  color: #999;
  margin-top: 6rpx;
}

.message-input {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1px solid #f0f0f0;
}

.message-input input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.send-button {
  margin-left: 20rpx;
  color: var(--primary-color);
  font-size: 28rpx;
}

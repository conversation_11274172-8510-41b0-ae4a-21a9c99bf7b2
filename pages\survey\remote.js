// pages/survey/remote.js
Page({
  data: {
    claimId: '',
    claimDetail: null,
    loading: true,
    connected: false,
    connecting: false,
    callDuration: 0,
    durationTimer: null,
    capturedImages: [],
    showGuide: true,
    guideSteps: [
      '请确保网络环境良好，建议使用WiFi连接',
      '请按照查勘员的指示拍摄相关照片',
      '通话过程中可以随时截图保存重要画面',
      '查勘完成后，系统会自动生成查勘报告'
    ],
    messages: [
      {
        id: 1,
        type: 'system',
        content: '查勘员李工已加入通话',
        time: '10:30'
      },
      {
        id: 2,
        type: 'other',
        content: '您好，我是查勘员李工，请按照我的指示进行拍摄',
        time: '10:31'
      }
    ],
    newMessage: ''
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        claimId: options.id
      })

      this.loadClaimDetail(options.id)
    }
  },

  onUnload: function () {
    // 清除计时器
    if (this.data.durationTimer) {
      clearInterval(this.data.durationTimer)
    }
  },

  loadClaimDetail: function (id) {
    this.setData({
      loading: true
    })

    // 模拟获取理赔详情
    setTimeout(() => {
      // 模拟数据
      const claimDetail = {
        id: id,
        status: 2, // 查勘中
        policy: {
          id: 'P20230001',
          name: '车险-京A12345',
          company: '现代财险',
          expireDate: '2023-12-31'
        },
        accident: {
          date: '2023-05-15',
          time: '10:30',
          location: '北京市朝阳区三里屯太古里',
          type: '车险',
          description: '交通事故车辆受损，右前车门凹陷，前保险杠刮擦。'
        }
      }

      this.setData({
        claimDetail,
        loading: false
      })
    }, 1500)
  },

  closeGuide: function () {
    this.setData({
      showGuide: false
    })
  },

  startCall: function () {
    this.setData({
      connecting: true
    })

    // 模拟连接
    setTimeout(() => {
      this.setData({
        connected: true,
        connecting: false
      })

      // 开始计时
      this.startTimer()

      // 模拟消息
      this.simulateMessages()
    }, 2000)
  },

  endCall: function () {
    wx.showModal({
      title: '结束查勘',
      content: '确定要结束远程查勘吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除计时器
          if (this.data.durationTimer) {
            clearInterval(this.data.durationTimer)
          }

          // 跳转到结果页
          wx.redirectTo({
            url: `/pages/survey/result?id=${this.data.claimId}`
          })
        }
      }
    })
  },

  startTimer: function () {
    const timer = setInterval(() => {
      this.setData({
        callDuration: this.data.callDuration + 1
      })
    }, 1000)

    this.setData({
      durationTimer: timer
    })
  },

  formatDuration: function (seconds) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  captureImage: function () {
    // 模拟拍照
    const images = this.data.capturedImages
    const newImage = {
      id: images.length + 1,
      url: `https://picsum.photos/400/300?random=${50 + images.length}`,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    }

    images.push(newImage)

    this.setData({
      capturedImages: images
    })

    wx.showToast({
      title: '已截图',
      icon: 'success'
    })
  },

  previewImage: function (e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.capturedImages.map(img => img.url)

    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },

  inputMessage: function (e) {
    this.setData({
      newMessage: e.detail.value
    })
  },

  sendMessage: function () {
    if (!this.data.newMessage.trim()) {
      return
    }

    const messages = this.data.messages
    const newMessage = {
      id: messages.length + 1,
      type: 'self',
      content: this.data.newMessage,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    }

    messages.push(newMessage)

    this.setData({
      messages,
      newMessage: ''
    })

    // 自动滚动到底部
    this.scrollToBottom()
  },

  scrollToBottom: function () {
    setTimeout(() => {
      wx.createSelectorQuery()
        .select('#message-container')
        .boundingClientRect(function (rect) {
          wx.pageScrollTo({
            scrollTop: rect.height,
            duration: 300
          })
        })
        .exec()
    }, 100)
  },

  simulateMessages: function () {
    // 模拟收到消息
    setTimeout(() => {
      const messages = this.data.messages
      messages.push({
        id: messages.length + 1,
        type: 'other',
        content: '请先拍摄车辆受损部位的整体照片',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      })

      this.setData({
        messages
      })

      this.scrollToBottom()
    }, 5000)

    setTimeout(() => {
      const messages = this.data.messages
      messages.push({
        id: messages.length + 1,
        type: 'other',
        content: '然后请拍摄车牌照片，确保清晰可见',
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      })

      this.setData({
        messages
      })

      this.scrollToBottom()
    }, 10000)
  }
})

export declare const imageProps: {
    sizeType: {
        type: ArrayConstructor;
        value: string[];
    };
    capture: {
        type: ArrayConstructor;
        value: string[];
    };
    showmenu: {
        type: BooleanConstructor;
        value: boolean;
    };
};
export declare const videoProps: {
    capture: {
        type: ArrayConstructor;
        value: string[];
    };
    compressed: {
        type: BooleanConstructor;
        value: boolean;
    };
    maxDuration: {
        type: NumberConstructor;
        value: number;
    };
    camera: {
        type: StringConstructor;
        value: string;
    };
    referrerPolicy: {
        type: StringConstructor;
        value: string;
    };
};
export declare const mediaProps: {
    capture: {
        type: ArrayConstructor;
        value: string[];
    };
    mediaType: {
        type: ArrayConstructor;
        value: string[];
    };
    maxDuration: {
        type: NumberConstructor;
        value: number;
    };
    camera: {
        type: StringConstructor;
        value: string;
    };
};
export declare const messageFileProps: {
    extension: null;
    previewFile: {
        type: BooleanConstructor;
        value: boolean;
    };
};

// pages/login/login.js
const app = getApp()

Page({
  data: {
    phone: '',
    code: '',
    countdown: 0,
    canGetCode: true,
    agreementChecked: false
  },

  onLoad: function (options) {
    // 页面加载
  },

  inputPhone: function (e) {
    this.setData({
      phone: e.detail.value
    })
  },

  inputCode: function (e) {
    this.setData({
      code: e.detail.value
    })
  },

  toggleAgreement: function () {
    this.setData({
      agreementChecked: !this.data.agreementChecked
    })
  },

  getVerificationCode: function () {
    const phone = this.data.phone
    
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (!/^1\d{10}$/.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return
    }
    
    if (!this.data.canGetCode) {
      return
    }
    
    // 发送验证码请求
    wx.showLoading({
      title: '发送中...',
    })
    
    // 模拟请求
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      
      // 开始倒计时
      this.setData({
        canGetCode: false,
        countdown: 60
      })
      
      this.startCountdown()
    }, 1000)
  },
  
  startCountdown: function () {
    const timer = setInterval(() => {
      if (this.data.countdown <= 1) {
        clearInterval(timer)
        this.setData({
          canGetCode: true,
          countdown: 0
        })
        return
      }
      
      this.setData({
        countdown: this.data.countdown - 1
      })
    }, 1000)
  },
  
  login: function () {
    const { phone, code, agreementChecked } = this.data
    
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (!/^1\d{10}$/.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return
    }
    
    if (!code) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }
    
    if (!agreementChecked) {
      wx.showToast({
        title: '请阅读并同意用户协议',
        icon: 'none'
      })
      return
    }
    
    // 登录请求
    wx.showLoading({
      title: '登录中...',
    })
    
    // 模拟请求
    setTimeout(() => {
      wx.hideLoading()
      
      // 登录成功
      app.globalData.isLogin = true
      app.globalData.token = 'mock_token_' + Date.now()
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
      // 返回上一页或首页
      setTimeout(() => {
        wx.navigateBack({
          delta: 1,
          fail: function() {
            wx.switchTab({
              url: '/pages/index/index',
            })
          }
        })
      }, 1000)
    }, 1500)
  },
  
  navigateToAgreement: function () {
    wx.navigateTo({
      url: '/pages/agreement/agreement',
    })
  }
})

/* pages/user/user.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

/* 用户信息 */
.user-header {
  background-color: var(--primary-color);
  padding: 40rpx 30rpx;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #fff;
  margin-right: 30rpx;
}

.avatar {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #e0e0e0;
}

.user-detail {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  opacity: 0.8;
}

.login-tip {
  font-size: 24rpx;
  opacity: 0.8;
}

.login-button button {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid #fff;
  font-size: 28rpx;
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
}

/* 统计信息 */
.statistics {
  display: flex;
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: #e0e0e0;
}

.stat-number {
  font-size: 40rpx;
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

/* 功能列表 */
.function-list {
  
}

.function-group {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.function-title {
  font-size: 32rpx;
  font-weight: 500;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.function-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.function-value {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
}

.function-arrow {
  color: #ccc;
}

/* 退出登录 */
.logout-section {
  padding: 30rpx;
  margin-top: 40rpx;
}

.logout-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #fff;
  color: #f5222d;
  font-size: 32rpx;
  border-radius: 45rpx;
}

/* 页脚 */
.footer {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 40rpx;
}

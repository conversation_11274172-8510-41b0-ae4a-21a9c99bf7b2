// pages/loss/assessment.js
Page({
  data: {
    claimId: '',
    claimDetail: null,
    loading: true,
    analyzing: false,
    analyzed: false,
    uploadedImages: [],
    maxImageCount: 9,
    lossItems: [],
    totalAmount: 0,
    remarks: '',
    submitting: false
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        claimId: options.id
      })

      this.loadClaimDetail(options.id)
    }
  },

  loadClaimDetail: function (id) {
    this.setData({
      loading: true
    })

    // 模拟获取理赔详情
    setTimeout(() => {
      // 模拟数据
      const claimDetail = {
        id: id,
        status: 2, // 查勘中
        policy: {
          id: 'P20230001',
          name: '车险-京A12345',
          company: '现代财险',
          expireDate: '2023-12-31'
        },
        accident: {
          date: '2023-05-15',
          time: '10:30',
          location: '北京市朝阳区三里屯太古里',
          type: '车险',
          description: '交通事故车辆受损，右前车门凹陷，前保险杠刮擦。'
        },
        images: [
          { url: 'https://picsum.photos/400/300?random=30', name: '车辆受损照片1' },
          { url: 'https://picsum.photos/400/300?random=31', name: '车辆受损照片2' },
          { url: 'https://picsum.photos/400/300?random=32', name: '事故现场' }
        ]
      }

      this.setData({
        claimDetail,
        loading: false,
        uploadedImages: claimDetail.images
      })
    }, 1500)
  },

  afterRead: function (e) {
    const { file } = e.detail
    const { uploadedImages } = this.data

    // 模拟上传
    const newImages = file.map(item => {
      return {
        url: item.url,
        name: '新上传照片' + (uploadedImages.length + 1)
      }
    })

    this.setData({
      uploadedImages: [...uploadedImages, ...newImages]
    })
  },

  deleteImage: function (e) {
    const { index } = e.detail
    const { uploadedImages } = this.data
    uploadedImages.splice(index, 1)
    this.setData({
      uploadedImages
    })
  },

  previewImage: function (e) {
    const { index } = e.currentTarget.dataset
    const urls = this.data.uploadedImages.map(img => img.url)

    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },

  startAnalysis: function () {
    if (this.data.uploadedImages.length === 0) {
      wx.showToast({
        title: '请上传照片',
        icon: 'none'
      })
      return
    }

    this.setData({
      analyzing: true
    })

    // 模拟AI分析
    setTimeout(() => {
      // 模拟分析结果
      const lossItems = [
        {
          id: 1,
          name: '右前车门更换',
          price: 2800,
          quantity: 1,
          amount: 2800,
          remark: '原厂件'
        },
        {
          id: 2,
          name: '前保险杠修复',
          price: 1200,
          quantity: 1,
          amount: 1200,
          remark: '局部喷漆'
        },
        {
          id: 3,
          name: '工时费',
          price: 100,
          quantity: 8,
          amount: 800,
          remark: '标准工时'
        }
      ]

      const totalAmount = lossItems.reduce((sum, item) => sum + item.amount, 0)

      this.setData({
        lossItems,
        totalAmount,
        analyzing: false,
        analyzed: true
      })
    }, 3000)
  },

  inputRemarks: function (e) {
    this.setData({
      remarks: e.detail.value
    })
  },

  submitAssessment: function () {
    if (!this.data.analyzed) {
      wx.showToast({
        title: '请先进行定损分析',
        icon: 'none'
      })
      return
    }

    this.setData({
      submitting: true
    })

    // 模拟提交
    setTimeout(() => {
      this.setData({
        submitting: false
      })

      wx.showToast({
        title: '提交成功',
        icon: 'success'
      })

      // 跳转到确认页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/loss/confirm?id=${this.data.claimId}`
        })
      }, 1500)
    }, 2000)
  }
})

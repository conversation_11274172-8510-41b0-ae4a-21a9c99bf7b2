/* pages/index/index.wxss */
.container {
  padding: 30rpx;
}

/* 用户信息栏 */
.user-info-bar {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  background: var(--gradient-primary);
  padding: 30rpx;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  color: #fff;
  margin: 0 -30rpx 30rpx -30rpx;
  position: relative;
  overflow: hidden;
}

.user-info-bar::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.user-greeting {
  flex: 1;
  font-size: 34rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.login-btn button {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 26rpx;
  padding: 0 24rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
}

/* 轮播图 */
.banner-section {
  margin-bottom: 30rpx;
  border-radius: var(--border-radius-base);
  overflow: hidden;
  height: 300rpx;
}

.banner-section swiper {
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 快捷功能区 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 20rpx;
  background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%);
  border-radius: var(--border-radius-large);
  margin-bottom: 30rpx;
  box-shadow: var(--shadow-light);
  border: 1px solid rgba(255, 107, 53, 0.08);
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  position: relative;
  transition: all 0.3s ease;
}

.quick-action-item:active {
  transform: scale(0.95);
}

.quick-action-icon {
  width: 88rpx;
  height: 88rpx;
  margin-bottom: 12rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 138, 101, 0.1) 100%);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.15);
}

.quick-action-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 我的理赔 */
.my-claims {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.view-all {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: normal;
}

.claims-list {
  min-height: 300rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  color: #999;
}

.claim-item {
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.claim-item:last-child {
  border-bottom: none;
}

.claim-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.claim-id {
  font-weight: 500;
}

.claim-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-1 {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-2 {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-3 {
  background-color: #f9f0ff;
  color: #722ed1;
}

.status-4 {
  background-color: #fcffe6;
  color: #7cb305;
}

.status-5 {
  background-color: #e6fffb;
  color: #13c2c2;
}

.status-6 {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-7 {
  background-color: #fff1f0;
  color: #f5222d;
}

.claim-info {
  display: flex;
  margin-bottom: 10rpx;
}

.claim-type, .claim-date {
  margin-right: 30rpx;
  font-size: 26rpx;
}

.claim-desc {
  font-size: 26rpx;
  color: #666;
}

.label {
  color: #999;
  margin-right: 10rpx;
}

.empty-claims {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-claims text {
  color: #999;
  margin-bottom: 30rpx;
}

/* 服务指南 */
.service-guide {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
}

.guide-list {

}

.guide-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-title {
  font-size: 28rpx;
}

.guide-arrow {
  color: #ccc;
}

/* pages/index/index.wxss */
.container {
  padding: 30rpx;
}

/* 用户信息栏 */
.user-info-bar {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #e0e0e0;
  border-radius: 50%;
}

.user-greeting {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
}

.login-btn button {
  background-color: var(--primary-color);
  color: #fff;
  font-size: 24rpx;
  padding: 0 20rpx;
}

/* 轮播图 */
.banner-section {
  margin-bottom: 30rpx;
  border-radius: var(--border-radius-base);
  overflow: hidden;
  height: 300rpx;
}

.banner-section swiper {
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 快捷功能区 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 0;
  background-color: #fff;
  border-radius: var(--border-radius-base);
  margin-bottom: 30rpx;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.quick-action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.quick-action-name {
  font-size: 26rpx;
  color: #333;
}

/* 我的理赔 */
.my-claims {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.view-all {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: normal;
}

.claims-list {
  min-height: 300rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  color: #999;
}

.claim-item {
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.claim-item:last-child {
  border-bottom: none;
}

.claim-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.claim-id {
  font-weight: 500;
}

.claim-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-1 {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-2 {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-3 {
  background-color: #f9f0ff;
  color: #722ed1;
}

.status-4 {
  background-color: #fcffe6;
  color: #7cb305;
}

.status-5 {
  background-color: #e6fffb;
  color: #13c2c2;
}

.status-6 {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-7 {
  background-color: #fff1f0;
  color: #f5222d;
}

.claim-info {
  display: flex;
  margin-bottom: 10rpx;
}

.claim-type, .claim-date {
  margin-right: 30rpx;
  font-size: 26rpx;
}

.claim-desc {
  font-size: 26rpx;
  color: #666;
}

.label {
  color: #999;
  margin-right: 10rpx;
}

.empty-claims {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-claims text {
  color: #999;
  margin-bottom: 30rpx;
}

/* 服务指南 */
.service-guide {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
}

.guide-list {
  
}

.guide-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-title {
  font-size: 28rpx;
}

.guide-arrow {
  color: #ccc;
}

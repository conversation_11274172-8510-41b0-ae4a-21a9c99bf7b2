<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./index.wxs" module="computed" />

<view class="{{ utils.bem('checkbox', [{ horizontal: direction === 'horizontal' }]) }} custom-class">
  <view
    wx:if="{{ labelPosition === 'left' }}"
    class="label-class {{ utils.bem('checkbox__label', [labelPosition, { disabled: disabled || parentDisabled }]) }}"
    bindtap="onClickLabel"
  >
    <slot />
  </view>
  <view class="van-checkbox__icon-wrap" bindtap="toggle">
    <slot wx:if="{{ useIconSlot }}" name="icon" />
    <van-icon
      wx:else
      name="success"
      size="0.8em"
      class="{{ utils.bem('checkbox__icon', [shape, { disabled: disabled || parentDisabled, checked: value }]) }}"
      style="{{ computed.iconStyle(checkedColor, value, disabled, parentDisabled, iconSize) }}"
      custom-class="icon-class"
      custom-style="line-height: 1.25em;"
    />
  </view>
  <view
    wx:if="{{ labelPosition === 'right' }}"
    class="label-class {{ utils.bem('checkbox__label', [labelPosition, { disabled: disabled || parentDisabled }]) }}"
    bindtap="onClickLabel"
  >
    <slot />
  </view>
</view>

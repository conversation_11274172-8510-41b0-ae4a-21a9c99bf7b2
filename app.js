// app.js
App({
  onLaunch: function () {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        console.log('登录成功', res)
      }
    })
    
    // 获取用户信息
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取头像昵称，不会弹框
          wx.getUserInfo({
            success: res => {
              // 可以将 res 发送给后台解码出 unionId
              this.globalData.userInfo = res.userInfo

              // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
              // 所以此处加入 callback 以防止这种情况
              if (this.userInfoReadyCallback) {
                this.userInfoReadyCallback(res)
              }
            }
          })
        }
      }
    })
  },
  globalData: {
    userInfo: null,
    isLogin: false,
    token: '',
    baseUrl: 'https://api.claimease.com',
    currentClaim: null,
    insuranceTypes: [
      { id: 1, name: '车险' },
      { id: 2, name: '健康险' },
      { id: 3, name: '财产险' },
      { id: 4, name: '意外险' },
      { id: 5, name: '旅游险' }
    ],
    claimStatus: {
      1: '已报案',
      2: '查勘中',
      3: '定损中',
      4: '理赔审核中',
      5: '待赔付',
      6: '已赔付',
      7: '已拒赔'
    }
  }
})

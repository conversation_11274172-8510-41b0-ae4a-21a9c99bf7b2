// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    claimList: [],
    loading: true,
    banners: [
      {
        id: 1,
        imageUrl: '/images/banner/banner1.png',
        linkUrl: '/pages/report/form'
      },
      {
        id: 2,
        imageUrl: '/images/banner/banner2.png',
        linkUrl: '/pages/survey/survey'
      },
      {
        id: 3,
        imageUrl: '/images/banner/banner3.png',
        linkUrl: '/pages/loss/loss'
      }
    ],
    quickActions: [
      {
        id: 1,
        name: '自助报案',
        icon: '/images/icons/report.png',
        url: '/pages/report/form'
      },
      {
        id: 2,
        name: '查勘预约',
        icon: '/images/icons/survey.png',
        url: '/pages/survey/appointment'
      },
      {
        id: 3,
        name: '定损评估',
        icon: '/images/icons/loss.png',
        url: '/pages/loss/assessment'
      },
      {
        id: 4,
        name: '理赔进度',
        icon: '/images/icons/progress.png',
        url: '/pages/report/report'
      }
    ]
  },
  
  onLoad: function () {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    } else if (this.data.canIUse) {
      // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
      // 所以此处加入 callback 以防止这种情况
      app.userInfoReadyCallback = res => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    } else {
      // 在没有 open-type=getUserInfo 版本的兼容处理
      wx.getUserInfo({
        success: res => {
          app.globalData.userInfo = res.userInfo
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          })
        }
      })
    }
    
    // 模拟获取理赔列表
    setTimeout(() => {
      this.setData({
        claimList: [
          {
            id: 'CL20230001',
            type: '车险',
            status: 2,
            date: '2023-05-15',
            description: '交通事故车辆受损'
          },
          {
            id: 'CL20230002',
            type: '财产险',
            status: 4,
            date: '2023-05-10',
            description: '家庭财产水渍损失'
          }
        ],
        loading: false
      })
    }, 1000)
  },
  
  getUserInfo: function(e) {
    console.log(e)
    app.globalData.userInfo = e.detail.userInfo
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },
  
  navigateTo: function(e) {
    const url = e.currentTarget.dataset.url
    wx.navigateTo({
      url: url
    })
  },
  
  viewClaimDetail: function(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/report/detail?id=${id}`
    })
  },
  
  onPullDownRefresh: function() {
    // 模拟刷新数据
    this.setData({
      loading: true
    })
    
    setTimeout(() => {
      this.setData({
        loading: false
      })
      wx.stopPullDownRefresh()
    }, 1000)
  }
})

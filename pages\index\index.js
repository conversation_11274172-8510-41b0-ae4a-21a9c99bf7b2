// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    claimList: [],
    loading: true,
    banners: [
      {
        id: 1,
        imageUrl: 'https://picsum.photos/750/300?random=1',
        linkUrl: '/pages/report/form'
      },
      {
        id: 2,
        imageUrl: 'https://picsum.photos/750/300?random=2',
        linkUrl: '/pages/survey/survey'
      },
      {
        id: 3,
        imageUrl: 'https://picsum.photos/750/300?random=3',
        linkUrl: '/pages/loss/loss'
      }
    ],
    quickActions: [
      {
        id: 1,
        name: '自助报案',
        icon: 'https://img.icons8.com/fluency/96/000000/file.png',
        url: '/pages/report/form'
      },
      {
        id: 2,
        name: '查勘预约',
        icon: 'https://img.icons8.com/fluency/96/000000/search.png',
        url: '/pages/survey/appointment'
      },
      {
        id: 3,
        name: '定损评估',
        icon: 'https://img.icons8.com/fluency/96/000000/calculator.png',
        url: '/pages/loss/assessment'
      },
      {
        id: 4,
        name: '理赔进度',
        icon: 'https://img.icons8.com/fluency/96/000000/progress-bar.png',
        url: '/pages/report/report'
      }
    ]
  },

  onLoad: function () {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    } else if (this.data.canIUse) {
      // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
      // 所以此处加入 callback 以防止这种情况
      app.userInfoReadyCallback = res => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    } else {
      // 在没有 open-type=getUserInfo 版本的兼容处理
      wx.getUserInfo({
        success: res => {
          app.globalData.userInfo = res.userInfo
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          })
        }
      })
    }

    // 模拟获取理赔列表
    setTimeout(() => {
      this.setData({
        claimList: [
          {
            id: 'CL20230001',
            type: '车险',
            status: 2,
            date: '2023-05-15',
            description: '交通事故车辆受损'
          },
          {
            id: 'CL20230002',
            type: '财产险',
            status: 4,
            date: '2023-05-10',
            description: '家庭财产水渍损失'
          }
        ],
        loading: false
      })
    }, 1000)
  },

  getUserInfo: function(e) {
    console.log(e)
    app.globalData.userInfo = e.detail.userInfo
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },

  navigateTo: function(e) {
    const url = e.currentTarget.dataset.url
    wx.navigateTo({
      url: url
    })
  },

  viewClaimDetail: function(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/report/detail?id=${id}`
    })
  },

  onPullDownRefresh: function() {
    // 模拟刷新数据
    this.setData({
      loading: true
    })

    setTimeout(() => {
      this.setData({
        loading: false
      })
      wx.stopPullDownRefresh()
    }, 1000)
  }
})

<!--pages/loss/assessment.wxml-->
<view class="container">
  <view class="header">
    <text class="title">定损评估</text>
    <text class="claim-id">报案号：{{ claimId }}</text>
  </view>
  
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading color="#1890FF" size="24px">加载中...</van-loading>
  </view>
  
  <block wx:else>
    <view class="claim-info">
      <view class="info-item">
        <text class="info-label">险种</text>
        <text class="info-value">{{ claimDetail.accident.type }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">出险时间</text>
        <text class="info-value">{{ claimDetail.accident.date }} {{ claimDetail.accident.time }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">事故描述</text>
        <text class="info-value">{{ claimDetail.accident.description }}</text>
      </view>
    </view>
    
    <view class="upload-section">
      <view class="section-title">
        <text>上传定损照片</text>
        <text class="section-subtitle">请上传清晰的损失物品照片，最多9张</text>
      </view>
      
      <van-uploader
        file-list="{{ uploadedImages }}"
        max-count="{{ maxImageCount }}"
        bind:after-read="afterRead"
        bind:delete="deleteImage"
        accept="image"
        multiple
        use-before-read="{{ false }}"
        image-fit="aspectFill"
      />
      
      <view class="upload-tips">
        <view class="tip-item">· 车险：请上传受损部位的近距离清晰照片</view>
        <view class="tip-item">· 财产险：请上传受损物品的整体和细节照片</view>
        <view class="tip-item">· 健康险：请上传医疗单据和诊断证明照片</view>
      </view>
    </view>
    
    <view class="analysis-section">
      <button 
        class="analysis-button {{ analyzing ? 'disabled' : '' }}" 
        bindtap="startAnalysis" 
        loading="{{ analyzing }}" 
        disabled="{{ analyzing }}"
      >
        {{ analyzing ? '分析中...' : '开始AI定损分析' }}
      </button>
      
      <view class="analysis-tips">
        <text>AI定损分析将基于您上传的照片，自动识别损失项目并估算金额</text>
      </view>
    </view>
    
    <view class="result-section" wx:if="{{ analyzed }}">
      <view class="section-title">定损结果</view>
      
      <view class="result-table">
        <view class="table-header">
          <view class="th th-name">项目名称</view>
          <view class="th th-price">单价</view>
          <view class="th th-quantity">数量</view>
          <view class="th th-amount">金额</view>
        </view>
        
        <view class="table-body">
          <view class="table-row" wx:for="{{ lossItems }}" wx:key="id">
            <view class="td td-name">
              <text>{{ item.name }}</text>
              <text class="td-remark">{{ item.remark }}</text>
            </view>
            <view class="td td-price">¥{{ item.price }}</view>
            <view class="td td-quantity">{{ item.quantity }}</view>
            <view class="td td-amount">¥{{ item.amount }}</view>
          </view>
        </view>
        
        <view class="table-footer">
          <text>合计金额</text>
          <text class="total-amount">¥{{ totalAmount }}</text>
        </view>
      </view>
      
      <view class="remarks-section">
        <text class="remarks-label">备注说明</text>
        <textarea 
          placeholder="请输入备注说明（选填）" 
          value="{{ remarks }}" 
          bindinput="inputRemarks"
          maxlength="200"
        ></textarea>
        <view class="textarea-counter">{{ remarks.length }}/200</view>
      </view>
    </view>
    
    <view class="submit-section" wx:if="{{ analyzed }}">
      <button 
        class="submit-button" 
        bindtap="submitAssessment" 
        loading="{{ submitting }}" 
        disabled="{{ submitting }}"
      >
        {{ submitting ? '提交中...' : '提交定损结果' }}
      </button>
    </view>
  </block>
</view>

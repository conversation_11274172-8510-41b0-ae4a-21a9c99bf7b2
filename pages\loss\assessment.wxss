/* pages/loss/assessment.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.claim-id {
  font-size: 28rpx;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.claim-info {
  background-color: #fff;
  margin: 20rpx 0;
  padding: 30rpx;
}

.info-item {
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.upload-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.section-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-left: 20rpx;
  font-weight: normal;
}

.upload-tips {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.tip-item {
  margin-bottom: 10rpx;
}

.analysis-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.analysis-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.analysis-button.disabled {
  background-color: #ccc;
  color: #fff;
}

.analysis-tips {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

.result-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.result-table {
  border: 1px solid #e8e8e8;
  border-radius: var(--border-radius-base);
  overflow: hidden;
  margin-top: 20rpx;
}

.table-header {
  display: flex;
  background-color: #f5f5f5;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.th {
  padding: 20rpx 10rpx;
  text-align: center;
}

.th-name {
  flex: 2;
  text-align: left;
}

.th-price, .th-quantity, .th-amount {
  flex: 1;
}

.table-body {
  
}

.table-row {
  display: flex;
  border-top: 1px solid #e8e8e8;
  font-size: 28rpx;
  color: #333;
}

.td {
  padding: 20rpx 10rpx;
  text-align: center;
}

.td-name {
  flex: 2;
  text-align: left;
  display: flex;
  flex-direction: column;
}

.td-remark {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.td-price, .td-quantity, .td-amount {
  flex: 1;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-top: 1px solid #e8e8e8;
  font-size: 28rpx;
  font-weight: 500;
}

.total-amount {
  color: #f5222d;
}

.remarks-section {
  margin-top: 30rpx;
}

.remarks-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

textarea {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: var(--border-radius-base);
  box-sizing: border-box;
  font-size: 28rpx;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.submit-section {
  padding: 30rpx;
}

.submit-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}

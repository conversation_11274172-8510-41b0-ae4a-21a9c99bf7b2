<wxs src="./index.wxs" module="computed"></wxs>
<wxs src="../../../wxs/utils.wxs" module="utils" />

<view class="van-calendar__month" style="{{ computed.getMonthStyle(visible, date, rowHeight) }}">
  <view wx:if="{{ showMonthTitle }}" class="van-calendar__month-title">
    {{ computed.formatMonthTitle(date) }}
  </view>

  <view wx:if="{{ visible }}" class="van-calendar__days">
    <view wx:if="{{ showMark }}" class="van-calendar__month-mark">
      {{ computed.getMark(date) }}
    </view>

    <view
      wx:for="{{ days }}"
      wx:key="index"
      style="{{ computed.getDayStyle(item.type, index, date, rowHeight, color, firstDayOfWeek) }}"
      class="{{ utils.bem('calendar__day', [item.type]) }} {{ item.className }}"
      data-index="{{ index }}"
      bindtap="onClick"
    >
      <view wx:if="{{ item.type === 'selected' }}" class="van-calendar__selected-day" style="width: {{ rowHeight }}px; height: {{ rowHeight }}px; background: {{ color }}">
        <view wx:if="{{ item.topInfo }}" class="van-calendar__top-info">{{ item.topInfo }}</view>
        {{ item.text }}
        <view wx:if="{{ item.bottomInfo }}" class="van-calendar__bottom-info">
          {{ item.bottomInfo }}
        </view>
      </view>

      <view wx:else>
        <view wx:if="{{ item.topInfo }}" class="van-calendar__top-info">{{ item.topInfo }}</view>
        {{ item.text }}
        <view wx:if="{{ item.bottomInfo }}" class="van-calendar__bottom-info">
          {{ item.bottomInfo }}
        </view>
      </view>
    </view>
  </view>
</view>

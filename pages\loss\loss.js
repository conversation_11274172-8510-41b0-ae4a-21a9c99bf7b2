// pages/loss/loss.js
const app = getApp()

Page({
  data: {
    activeTab: 0,
    loading: true,
    lossList: [],
    tabs: [
      { id: 0, name: '全部' },
      { id: 1, name: '评估中' },
      { id: 2, name: '已完成' }
    ]
  },

  onLoad: function (options) {
    this.loadLossList()
  },
  
  onShow: function () {
    // 每次显示页面时刷新数据
    this.loadLossList()
  },

  loadLossList: function () {
    this.setData({
      loading: true
    })
    
    // 模拟获取定损列表
    setTimeout(() => {
      const allLosses = [
        {
          id: '**********',
          claimId: '**********',
          type: '车险',
          status: 1, // 1-评估中, 2-已完成
          date: '2023-05-17',
          amount: 0,
          description: '交通事故车辆受损'
        },
        {
          id: '**********',
          claimId: '**********',
          type: '财产险',
          status: 1,
          date: '2023-05-12',
          amount: 0,
          description: '家庭财产水渍损失'
        },
        {
          id: '**********',
          claimId: '**********',
          type: '健康险',
          status: 2,
          date: '2023-04-22',
          amount: 5800,
          description: '住院医疗费用报销'
        }
      ]
      
      this.setData({
        lossList: allLosses,
        loading: false
      })
      
      this.filterLossesByTab()
    }, 1000)
  },
  
  onTabChange: function (e) {
    const activeTab = e.detail.index
    this.setData({
      activeTab
    })
    
    this.filterLossesByTab()
  },
  
  filterLossesByTab: function () {
    const { activeTab, lossList } = this.data
    
    if (activeTab === 0) {
      // 全部
      this.setData({
        filteredLossList: lossList
      })
    } else if (activeTab === 1) {
      // 评估中
      this.setData({
        filteredLossList: lossList.filter(loss => loss.status === 1)
      })
    } else if (activeTab === 2) {
      // 已完成
      this.setData({
        filteredLossList: lossList.filter(loss => loss.status === 2)
      })
    }
  },
  
  viewLossDetail: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/report/detail?id=${id}`
    })
  },
  
  startLossAssessment: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/loss/assessment?id=${id}`
    })
  },
  
  viewLossResult: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/loss/confirm?id=${id}`
    })
  },
  
  onPullDownRefresh: function () {
    this.loadLossList()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})

<!--pages/survey/result.wxml-->
<view class="container">
  <view class="header">
    <text class="title">查勘结果</text>
    <text class="claim-id">报案号：{{ claimId }}</text>
  </view>
  
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading color="#1890FF" size="24px">加载中...</van-loading>
  </view>
  
  <block wx:else>
    <view class="result-card">
      <view class="card-header">
        <view class="status-tag">已完成</view>
        <text class="survey-id">查勘编号：{{ surveyResult.id }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">查勘方式</text>
        <text class="info-value">{{ surveyResult.surveyType }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">查勘时间</text>
        <text class="info-value">{{ surveyResult.surveyTime }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">查勘员</text>
        <view class="info-value-contact">
          <text>{{ surveyResult.surveyor }}</text>
          <view class="contact-btn" bindtap="contactSurveyor">联系</view>
        </view>
      </view>
    </view>
    
    <view class="result-card">
      <view class="card-title">查勘结论</view>
      <view class="conclusion">{{ surveyResult.conclusion }}</view>
    </view>
    
    <view class="result-card">
      <view class="card-title">查勘照片</view>
      <view class="images-container">
        <view 
          class="image-item" 
          wx:for="{{ surveyResult.images }}" 
          wx:key="index"
          bindtap="previewImage"
          data-index="{{ index }}"
        >
          <image src="{{ item.url }}" mode="aspectFill"></image>
          <text class="image-name">{{ item.name }}</text>
        </view>
      </view>
    </view>
    
    <view class="result-card">
      <view class="card-title">后续处理</view>
      <view class="next-step">
        <text class="step-label">下一步</text>
        <text class="step-value">{{ surveyResult.nextStep }}</text>
      </view>
      <view class="remarks">
        <text class="remarks-label">备注</text>
        <text class="remarks-value">{{ surveyResult.remarks }}</text>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="btn-primary" bindtap="startLossAssessment">开始定损评估</button>
      <button class="btn-secondary" bindtap="backToDetail">返回详情</button>
    </view>
  </block>
</view>

/* pages/report/detail.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.claim-id {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

.copy-btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  padding: 4rpx 16rpx;
  border: 1px solid var(--primary-color);
  border-radius: 20rpx;
}

.claim-status {
  
}

.status-text {
  display: inline-block;
  font-size: 28rpx;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

.status-1 {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-2 {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-3 {
  background-color: #f9f0ff;
  color: #722ed1;
}

.status-4 {
  background-color: #fcffe6;
  color: #7cb305;
}

.status-5 {
  background-color: #e6fffb;
  color: #13c2c2;
}

.status-6 {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-7 {
  background-color: #fff1f0;
  color: #f5222d;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.progress-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 20rpx;
}

.info-label {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 30%;
  margin-right: 3%;
  margin-bottom: 20rpx;
}

.image-item image {
  width: 100%;
  height: 160rpx;
  border-radius: var(--border-radius-base);
}

.image-name {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.action-content {
  
}

.status-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.status-message text {
  display: block;
  margin-bottom: 10rpx;
}

.action-buttons {
  display: flex;
  margin-top: 20rpx;
}

.btn-primary, .btn-secondary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
  margin-left: 20rpx;
}

.timeline-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.timeline {
  
}

.timeline-item {
  position: relative;
  padding-left: 30rpx;
  padding-bottom: 30rpx;
  border-left: 1px solid #e8e8e8;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -6rpx;
  top: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.timeline-content {
  
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
}

.contact-section {
  padding: 30rpx;
}

.contact-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  background-color: #fff;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.contact-button van-icon {
  margin-right: 10rpx;
}

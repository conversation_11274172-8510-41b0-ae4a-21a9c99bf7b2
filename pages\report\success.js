// pages/report/success.js
Page({
  data: {
    claimId: '',
    currentTime: ''
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        claimId: options.id
      })
    }
    
    // 获取当前时间
    const now = new Date()
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    const hour = now.getHours().toString().padStart(2, '0')
    const minute = now.getMinutes().toString().padStart(2, '0')
    
    this.setData({
      currentTime: `${year}-${month}-${day} ${hour}:${minute}`
    })
  },
  
  viewClaimDetail: function () {
    wx.redirectTo({
      url: `/pages/report/detail?id=${this.data.claimId}`
    })
  },
  
  backToHome: function () {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },
  
  copyClaimId: function () {
    wx.setClipboardData({
      data: this.data.claimId,
      success: function () {
        wx.showToast({
          title: '报案号已复制',
          icon: 'success'
        })
      }
    })
  }
})

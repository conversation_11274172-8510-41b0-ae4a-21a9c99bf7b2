/* pages/survey/result.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  padding: 30rpx;
  background-color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.claim-id {
  font-size: 28rpx;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.result-card {
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: var(--border-radius-base);
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-tag {
  background-color: #f6ffed;
  color: #52c41a;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.survey-id {
  font-size: 26rpx;
  color: #666;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #999;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-value-contact {
  display: flex;
  align-items: center;
}

.contact-btn {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  padding: 4rpx 16rpx;
  border: 1px solid var(--primary-color);
  border-radius: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.conclusion {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 30%;
  margin-right: 3%;
  margin-bottom: 20rpx;
}

.image-item image {
  width: 100%;
  height: 160rpx;
  border-radius: var(--border-radius-base);
}

.image-name {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.next-step {
  margin-bottom: 20rpx;
}

.step-label {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
}

.step-value {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 500;
}

.remarks {
  
}

.remarks-label {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.remarks-value {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.action-buttons {
  padding: 30rpx;
}

.btn-primary, .btn-secondary {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

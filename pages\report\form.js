// pages/report/form.js
const app = getApp()

Page({
  data: {
    currentStep: 0,
    steps: [
      { text: '填写信息' },
      { text: '上传资料' },
      { text: '提交确认' }
    ],
    // 保单信息
    policyList: [
      { id: 'P20230001', name: '车险-京A12345', company: '平安保险', expireDate: '2023-12-31' },
      { id: 'P20230002', name: '家财险-北京市朝阳区', company: '太平洋保险', expireDate: '2024-05-20' },
      { id: 'P20230003', name: '健康险-个人医疗', company: '中国人寿', expireDate: '2024-03-15' }
    ],
    selectedPolicy: null,
    // 出险信息
    accidentDate: '',
    accidentTime: '',
    accidentLocation: '',
    accidentType: '',
    accidentDescription: '',
    // 上传资料
    fileList: [],
    maxFileCount: 9,
    // 表单验证
    errors: {},
    // 定位信息
    latitude: null,
    longitude: null,
    // 保险类型
    insuranceTypes: [],
    showInsuranceTypePicker: false,
    // 提交状态
    submitting: false
  },

  onLoad: function (options) {
    // 获取保险类型
    this.setData({
      insuranceTypes: app.globalData.insuranceTypes
    })
    
    // 获取当前位置
    this.getCurrentLocation()
  },
  
  // 步骤控制
  nextStep: function () {
    const { currentStep } = this.data
    
    if (currentStep === 0) {
      // 验证第一步表单
      if (!this.validateStep1()) {
        return
      }
    } else if (currentStep === 1) {
      // 验证第二步表单
      if (!this.validateStep2()) {
        return
      }
    }
    
    this.setData({
      currentStep: currentStep + 1
    })
  },
  
  prevStep: function () {
    const { currentStep } = this.data
    
    if (currentStep > 0) {
      this.setData({
        currentStep: currentStep - 1
      })
    }
  },
  
  // 表单验证
  validateStep1: function () {
    const { selectedPolicy, accidentDate, accidentTime, accidentLocation, accidentType, accidentDescription } = this.data
    let errors = {}
    let isValid = true
    
    if (!selectedPolicy) {
      errors.selectedPolicy = '请选择保单'
      isValid = false
    }
    
    if (!accidentDate) {
      errors.accidentDate = '请选择出险日期'
      isValid = false
    }
    
    if (!accidentTime) {
      errors.accidentTime = '请选择出险时间'
      isValid = false
    }
    
    if (!accidentLocation) {
      errors.accidentLocation = '请填写出险地点'
      isValid = false
    }
    
    if (!accidentType) {
      errors.accidentType = '请选择出险类型'
      isValid = false
    }
    
    if (!accidentDescription) {
      errors.accidentDescription = '请填写事故描述'
      isValid = false
    }
    
    this.setData({ errors })
    
    if (!isValid) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      })
    }
    
    return isValid
  },
  
  validateStep2: function () {
    const { fileList } = this.data
    let errors = {}
    let isValid = true
    
    if (fileList.length === 0) {
      errors.fileList = '请上传至少一张照片'
      isValid = false
    }
    
    this.setData({ errors })
    
    if (!isValid) {
      wx.showToast({
        title: '请上传资料',
        icon: 'none'
      })
    }
    
    return isValid
  },
  
  // 表单操作
  selectPolicy: function (e) {
    const index = e.currentTarget.dataset.index
    const policy = this.data.policyList[index]
    
    this.setData({
      selectedPolicy: policy,
      'errors.selectedPolicy': ''
    })
  },
  
  bindDateChange: function (e) {
    this.setData({
      accidentDate: e.detail.value,
      'errors.accidentDate': ''
    })
  },
  
  bindTimeChange: function (e) {
    this.setData({
      accidentTime: e.detail.value,
      'errors.accidentTime': ''
    })
  },
  
  inputLocation: function (e) {
    this.setData({
      accidentLocation: e.detail.value,
      'errors.accidentLocation': ''
    })
  },
  
  showInsuranceTypePicker: function () {
    this.setData({
      showInsuranceTypePicker: true
    })
  },
  
  onInsuranceTypePickerCancel: function () {
    this.setData({
      showInsuranceTypePicker: false
    })
  },
  
  onInsuranceTypePickerConfirm: function (e) {
    const { value, index } = e.detail
    const insuranceType = this.data.insuranceTypes[index]
    
    this.setData({
      accidentType: insuranceType.name,
      'errors.accidentType': '',
      showInsuranceTypePicker: false
    })
  },
  
  inputDescription: function (e) {
    this.setData({
      accidentDescription: e.detail.value,
      'errors.accidentDescription': ''
    })
  },
  
  // 文件上传
  afterRead: function (e) {
    const { file } = e.detail
    const { fileList = [] } = this.data
    
    this.setData({
      fileList: [...fileList, ...file],
      'errors.fileList': ''
    })
  },
  
  deleteFile: function (e) {
    const { index } = e.detail
    const { fileList } = this.data
    fileList.splice(index, 1)
    this.setData({
      fileList
    })
  },
  
  // 定位相关
  getCurrentLocation: function () {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const { latitude, longitude } = res
        this.setData({
          latitude,
          longitude
        })
        
        // 逆地址解析
        this.getLocationName(latitude, longitude)
      },
      fail: (err) => {
        console.error('获取位置失败', err)
      }
    })
  },
  
  getLocationName: function (latitude, longitude) {
    // 这里应该调用地图API进行逆地址解析
    // 为了演示，使用模拟数据
    this.setData({
      accidentLocation: '北京市朝阳区三里屯太古里'
    })
  },
  
  useCurrentLocation: function () {
    if (this.data.latitude && this.data.longitude) {
      this.getLocationName(this.data.latitude, this.data.longitude)
    } else {
      this.getCurrentLocation()
    }
  },
  
  // 提交报案
  submitClaim: function () {
    this.setData({
      submitting: true
    })
    
    // 模拟提交
    setTimeout(() => {
      this.setData({
        submitting: false
      })
      
      // 生成报案号
      const claimId = 'CL' + new Date().getTime().toString().substr(-8)
      
      // 跳转到成功页面
      wx.redirectTo({
        url: `/pages/report/success?id=${claimId}`
      })
    }, 2000)
  }
})

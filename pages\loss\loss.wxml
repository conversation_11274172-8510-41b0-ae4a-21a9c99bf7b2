<!--pages/loss/loss.wxml-->
<view class="container">
  <view class="header">
    <text class="title">定损管理</text>
  </view>
  
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" swipeable animated color="#1890FF">
    <van-tab wx:for="{{ tabs }}" wx:key="id" title="{{ item.name }}">
      <view class="loss-list">
        <view wx:if="{{ loading }}" class="loading-container">
          <van-loading color="#1890FF" size="24px">加载中...</van-loading>
        </view>
        
        <block wx:elif="{{ filteredLossList.length > 0 }}">
          <view class="loss-item" wx:for="{{ filteredLossList }}" wx:key="id">
            <view class="loss-header" bindtap="viewLossDetail" data-id="{{ item.claimId }}">
              <text class="loss-id">单号: {{ item.claimId }}</text>
              <text class="loss-status status-{{ item.status }}">{{ item.status === 1 ? '评估中' : '已完成' }}</text>
            </view>
            <view class="loss-info" bindtap="viewLossDetail" data-id="{{ item.claimId }}">
              <view class="loss-type">
                <text class="label">险种:</text>
                <text class="value">{{ item.type }}</text>
              </view>
              <view class="loss-date">
                <text class="label">定损日期:</text>
                <text class="value">{{ item.date }}</text>
              </view>
            </view>
            <view class="loss-desc" bindtap="viewLossDetail" data-id="{{ item.claimId }}">
              <text class="label">事故描述:</text>
              <text class="value">{{ item.description }}</text>
            </view>
            <view class="loss-amount" wx:if="{{ item.status === 2 }}" bindtap="viewLossDetail" data-id="{{ item.claimId }}">
              <text class="label">定损金额:</text>
              <text class="value amount">¥{{ item.amount }}</text>
            </view>
            <view class="loss-actions" wx:if="{{ item.status === 1 }}">
              <button class="action-btn" bindtap="startLossAssessment" data-id="{{ item.claimId }}">开始评估</button>
            </view>
            <view class="loss-actions" wx:else>
              <button class="action-btn" bindtap="viewLossResult" data-id="{{ item.claimId }}">查看结果</button>
            </view>
          </view>
        </block>
        
        <view wx:else class="empty-container">
          <image src="/images/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无定损记录</text>
        </view>
      </view>
    </van-tab>
  </van-tabs>
</view>
